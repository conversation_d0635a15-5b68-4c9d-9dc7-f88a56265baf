import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Typography,
  Button,
  Chip,
  Loading,
  Breadcrumb,
} from '@/shared/components/common';
import {
  ArrowLeft,
  Edit,
  Download,
  Trash2,
  Database,
  Calendar,
  User,
  FileText,
} from 'lucide-react';
import { useUserDataFineTuneDetail } from '../user-data-fine-tune/hooks/useUserDataFineTune';
import {
  DataFineTuneStatus,
  ProviderFineTuneEnum,
} from '../user-data-fine-tune/types/user-data-fine-tune.types';

/**
 * Dataset Detail Page - Hiển thị chi tiết dataset
 */
const DatasetDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const { t } = useTranslation();
  const navigate = useNavigate();

  const { data: dataset, isLoading, error } = useUserDataFineTuneDetail(id!);

  // Status chip colors
  const getStatusColor = (status: DataFineTuneStatus) => {
    switch (status) {
      case DataFineTuneStatus.COMPLETED:
        return 'success';
      case DataFineTuneStatus.PROCESSING:
        return 'warning';
      case DataFineTuneStatus.FAILED:
        return 'error';
      case DataFineTuneStatus.CANCELLED:
        return 'default';
      default:
        return 'info';
    }
  };

  // Status labels
  const getStatusLabel = (status: DataFineTuneStatus) => {
    switch (status) {
      case DataFineTuneStatus.PENDING:
        return t('Đang chờ');
      case DataFineTuneStatus.PROCESSING:
        return t('Đang xử lý');
      case DataFineTuneStatus.COMPLETED:
        return t('Hoàn thành');
      case DataFineTuneStatus.FAILED:
        return t('Thất bại');
      case DataFineTuneStatus.CANCELLED:
        return t('Đã hủy');
      default:
        return status;
    }
  };

  // Provider labels
  const getProviderLabel = (provider: ProviderFineTuneEnum) => {
    switch (provider) {
      case ProviderFineTuneEnum.OPENAI:
        return 'OpenAI';
      case ProviderFineTuneEnum.ANTHROPIC:
        return 'Anthropic';
      case ProviderFineTuneEnum.GOOGLE:
        return 'Google';
      default:
        return provider;
    }
  };

  // Format date
  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('vi-VN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loading size="lg" />
      </div>
    );
  }

  if (error || !dataset) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center py-8">
          <Typography variant="h5" className="text-red-600 mb-4">
            {t('Không tìm thấy dataset')}
          </Typography>
          <Button
            variant="outline"
            onClick={() => navigate('/user-dataset')}
          >
            {t('Quay lại danh sách')}
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Breadcrumb */}
      <Breadcrumb className="mb-6">
        <Breadcrumb.Item onClick={() => navigate('/user-dataset')}>
          {t('Dataset')}
        </Breadcrumb.Item>
        <Breadcrumb.Item>{dataset.name}</Breadcrumb.Item>
      </Breadcrumb>

      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-8">
        <div className="flex items-center">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate('/user-dataset')}
            className="mr-4"
          >
            <ArrowLeft size={16} />
          </Button>
          <div>
            <Typography variant="h3" className="flex items-center">
              <Database className="mr-3 text-primary" size={28} />
              {dataset.name}
            </Typography>
            <div className="flex items-center mt-2 space-x-4">
              <Chip
                color={getStatusColor(dataset.status)}
                size="md"
              >
                {getStatusLabel(dataset.status)}
              </Chip>
              <Typography variant="body2" className="text-gray-600">
                ID: {dataset.id}
              </Typography>
            </div>
          </div>
        </div>

        <div className="flex space-x-2">
          <Button
            variant="outline"
            onClick={() => navigate(`/user-dataset/${id}/edit`)}
            disabled={dataset.status === DataFineTuneStatus.PROCESSING}
          >
            <Edit size={16} className="mr-2" />
            {t('Chỉnh sửa')}
          </Button>
          <Button
            variant="outline"
            disabled={dataset.status !== DataFineTuneStatus.COMPLETED}
          >
            <Download size={16} className="mr-2" />
            {t('Tải xuống')}
          </Button>
          <Button
            variant="outline"
            className="text-red-600 hover:text-red-700"
            disabled={dataset.status === DataFineTuneStatus.PROCESSING}
          >
            <Trash2 size={16} className="mr-2" />
            {t('Xóa')}
          </Button>
        </div>
      </div>

      {/* Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Main Info */}
        <div className="lg:col-span-2 space-y-6">
          {/* Description */}
          <Card>
            <div className="p-6">
              <Typography variant="h6" className="mb-4 flex items-center">
                <FileText className="mr-2 text-gray-600" size={20} />
                {t('Mô tả')}
              </Typography>
              <Typography variant="body1" className="text-gray-700 leading-relaxed">
                {dataset.description || t('Không có mô tả')}
              </Typography>
            </div>
          </Card>

          {/* Dataset URLs */}
          <Card>
            <div className="p-6">
              <Typography variant="h6" className="mb-4">
                {t('Dữ liệu')}
              </Typography>
              <div className="space-y-4">
                <div>
                  <Typography variant="body2" className="font-medium text-gray-700 mb-2">
                    {t('Training Dataset')}
                  </Typography>
                  <div className="bg-gray-50 p-3 rounded-lg">
                    <Typography variant="body2" className="font-mono text-sm break-all">
                      {dataset.trainDatasetUrl}
                    </Typography>
                  </div>
                </div>
                
                {dataset.validDatasetUrl && (
                  <div>
                    <Typography variant="body2" className="font-medium text-gray-700 mb-2">
                      {t('Validation Dataset')}
                    </Typography>
                    <div className="bg-gray-50 p-3 rounded-lg">
                      <Typography variant="body2" className="font-mono text-sm break-all">
                        {dataset.validDatasetUrl}
                      </Typography>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Basic Info */}
          <Card>
            <div className="p-6">
              <Typography variant="h6" className="mb-4">
                {t('Thông tin cơ bản')}
              </Typography>
              <div className="space-y-4">
                <div className="flex items-center">
                  <User className="mr-3 text-gray-400" size={16} />
                  <div>
                    <Typography variant="body2" className="text-gray-600">
                      {t('Nhà cung cấp')}
                    </Typography>
                    <Typography variant="body1" className="font-medium">
                      {getProviderLabel(dataset.provider)}
                    </Typography>
                  </div>
                </div>

                <div className="flex items-center">
                  <Calendar className="mr-3 text-gray-400" size={16} />
                  <div>
                    <Typography variant="body2" className="text-gray-600">
                      {t('Ngày tạo')}
                    </Typography>
                    <Typography variant="body1" className="font-medium">
                      {formatDate(dataset.createdAt)}
                    </Typography>
                  </div>
                </div>
              </div>
            </div>
          </Card>

          {/* Status Details */}
          {dataset.status === DataFineTuneStatus.PROCESSING && (
            <Card>
              <div className="p-6">
                <Typography variant="h6" className="mb-4">
                  {t('Tiến độ xử lý')}
                </Typography>
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-6 w-6 border-2 border-primary border-t-transparent mr-3"></div>
                  <Typography variant="body1">
                    {t('Đang xử lý dữ liệu...')}
                  </Typography>
                </div>
              </div>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
};

export default DatasetDetailPage;
