import React from 'react';
import { RouteObject } from 'react-router-dom';
import { DatasetListPage, DatasetDetailPage, CreateDatasetPage } from '../pages';

/**
 * User Dataset Routes
 */
export const userDatasetRoutes: RouteObject[] = [
  {
    path: '/user-dataset',
    children: [
      {
        index: true,
        element: <DatasetListPage />,
      },
      {
        path: 'create',
        element: <CreateDatasetPage />,
      },
      {
        path: ':id',
        element: <DatasetDetailPage />,
      },
      {
        path: ':id/edit',
        element: <CreateDatasetPage />, // Reuse create page with edit mode
      },
    ],
  },
];

export default userDatasetRoutes;
