import { Loading } from '@/shared/components';
import MainLayout from '@/shared/layouts/MainLayout';
import { Suspense, lazy } from 'react';
import { RouteObject } from 'react-router-dom';
import UserDatasetManagementPage from '../pages/UserDatasetManagementPage';
import i18n from '@/lib/i18n';

// Import User Dataset module pages
const DataFineTunePage = lazy(() => import('@/modules/user-dataset/pages/DataFineTunePage'));

/**
 * User Dataset module routes
 */
const userDatasetRoutes: RouteObject[] = [
  {
    path: '/user-dataset',
    element: (
      <MainLayout title={i18n.t('user-dataset:title', 'Quản lý Dataset & Model')}>
        <Suspense fallback={<Loading />}>
          <UserDatasetManagementPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/user-dataset/data-fine-tune',
    element: (
      <MainLayout title="Dataset Fine-tune">
        <Suspense fallback={<Loading />}>
          <DataFineTunePage />
        </Suspense>
      </MainLayout>
    ),
  },
];

export default userDatasetRoutes;
