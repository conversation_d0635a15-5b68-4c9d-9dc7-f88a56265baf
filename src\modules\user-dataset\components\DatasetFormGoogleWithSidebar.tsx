import React, { useState, useCallback } from 'react';
import { Card, Input, Button, FormItem, Textarea } from '@/shared/components/common';
import { useTranslation } from 'react-i18next';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  CreateUserDataFineTuneDto,
  ProviderFineTuneEnum,
} from '../user-data-fine-tune/types/user-data-fine-tune.types';
import { useCreateAndUploadDataset } from '../user-data-fine-tune/hooks/useUserDataFineTune';
import {
  convertConversationsToJsonl,
  validateJsonlData,
} from '../user-data-fine-tune/services/user-data-fine-tune.service';
import ChatPanel from './ChatPanel';
import ConversationSidebarGoogle from './ConversationSidebarGoogle';
import {
  ImportedConversation,
  DatasetMessage,
} from '../user-data-fine-tune/types/user-data-fine-tune.types';

// Schema validation cho Google dataset form
const GoogleDatasetFormSchema = z.object({
  name: z.string().min(1, 'Dataset name is required'),
  description: z.string().min(1, 'Dataset description is required'),
});

type GoogleDatasetFormData = z.infer<typeof GoogleDatasetFormSchema>;

interface DatasetFormGoogleWithSidebarProps {
  /**
   * Callback khi tạo dataset thành công
   */
  onSuccess?: () => void;

  /**
   * Callback khi conversations thay đổi
   */
  onConversationsChange?: (conversations: ImportedConversation[]) => void;
}

/**
 * Component form tạo dataset cho Google với ConversationSidebarGoogle
 * Giao diện: ConversationSidebarGoogle + ChatPanel + DatasetForm
 * Không có import file JSON
 */
const DatasetFormGoogleWithSidebar: React.FC<DatasetFormGoogleWithSidebarProps> = ({
  onSuccess,
  onConversationsChange,
}) => {
  const { t } = useTranslation();
  const { createAndUpload, isLoading } = useCreateAndUploadDataset();

  // State cho conversations và dataset
  const [conversations, setConversations] = useState<ImportedConversation[]>([]);
  const [selectedConversationId, setSelectedConversationId] = useState<string | null>(null);
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);
  const [showForm, setShowForm] = useState(false);

  // Get selected conversation
  const selectedConversation = conversations.find(conv => conv.id === selectedConversationId);

  // Khởi tạo form với React Hook Form và Zod
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<GoogleDatasetFormData>({
    resolver: zodResolver(GoogleDatasetFormSchema),
    defaultValues: {
      name: '',
      description: '',
    },
  });

  // Generate unique ID
  const generateId = (): string => {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  };

  // Generate conversation title from first user message
  const generateTitle = (messages: DatasetMessage[]): string => {
    const firstUserMessage = messages.find(msg => msg.role === 'user');
    if (firstUserMessage) {
      return firstUserMessage.content.length > 30
        ? firstUserMessage.content.substring(0, 30) + '...'
        : firstUserMessage.content;
    }
    return 'New Conversation';
  };

  // Handle conversations change
  const handleConversationsChange = useCallback(
    (updatedConversations: ImportedConversation[]) => {
      setConversations(updatedConversations);
      // Notify parent component
      if (onConversationsChange) {
        onConversationsChange(updatedConversations);
      }
    },
    [onConversationsChange]
  );

  // Handle new chat
  const handleNewChat = useCallback(() => {
    const newConversation: ImportedConversation = {
      id: generateId(),
      title: 'New Chat',
      messages: [],
      createdAt: new Date(),
    };

    const updatedConversations = [newConversation, ...conversations];
    setConversations(updatedConversations);
    setSelectedConversationId(newConversation.id);
    handleConversationsChange(updatedConversations);
  }, [conversations, handleConversationsChange]);

  // Handle select conversation
  const handleSelectConversation = useCallback((conversationId: string) => {
    setSelectedConversationId(conversationId);
  }, []);

  // Handle delete conversation
  const handleDeleteConversation = useCallback(
    (conversationId: string) => {
      const updatedConversations = conversations.filter(conv => conv.id !== conversationId);
      setConversations(updatedConversations);

      // If deleted conversation was selected, select another one
      if (selectedConversationId === conversationId) {
        setSelectedConversationId(
          updatedConversations.length > 0 ? updatedConversations[0].id : null
        );
      }

      handleConversationsChange(updatedConversations);
    },
    [conversations, selectedConversationId, handleConversationsChange]
  );

  // Handle toggle sidebar
  const handleToggleSidebar = useCallback(() => {
    setIsSidebarOpen(prev => !prev);
  }, []);

  // Handle add message to selected conversation
  const handleAddMessage = useCallback(
    (message: DatasetMessage) => {
      if (!selectedConversationId) return;

      const updatedConversations = conversations.map(conv => {
        if (conv.id === selectedConversationId) {
          const newMessages = [...conv.messages, message];
          let newTitle = conv.title;
          if (conv.messages.length === 0 && message.role === 'user') {
            newTitle = generateTitle([message]);
          }
          return { ...conv, title: newTitle, messages: newMessages };
        }
        return conv;
      });

      setConversations(updatedConversations);
      handleConversationsChange(updatedConversations);
    },
    [selectedConversationId, conversations, handleConversationsChange]
  );

  // Handle delete message
  const handleDeleteMessage = useCallback(
    (messageIndex: number) => {
      if (!selectedConversationId) return;

      const updatedConversations = conversations.map(conv => {
        if (conv.id === selectedConversationId) {
          const newMessages = [...conv.messages];
          newMessages.splice(messageIndex, 1);
          return { ...conv, messages: newMessages };
        }
        return conv;
      });

      setConversations(updatedConversations);
      handleConversationsChange(updatedConversations);
    },
    [selectedConversationId, conversations, handleConversationsChange]
  );

  // Handle edit message
  const handleEditMessage = useCallback(
    (messageIndex: number, message: DatasetMessage) => {
      if (!selectedConversationId) return;

      const updatedConversations = conversations.map(conv => {
        if (conv.id === selectedConversationId) {
          const newMessages = [...conv.messages];
          newMessages[messageIndex] = message;
          return { ...conv, messages: newMessages };
        }
        return conv;
      });

      setConversations(updatedConversations);
      handleConversationsChange(updatedConversations);
    },
    [selectedConversationId, conversations, handleConversationsChange]
  );

  // Xử lý submit form
  const onSubmit = async (data: GoogleDatasetFormData) => {
    try {
      if (conversations.length === 0) {
        console.error('No conversations available');
        return;
      }

      // Convert conversations to JSONL format
      const trainJsonlData = convertConversationsToJsonl(conversations);

      // Validate JSONL data
      const validation = validateJsonlData(trainJsonlData);
      if (!validation.isValid) {
        console.error('Training data validation failed:', validation.errors);
        return;
      }

      // Create dataset info for Google
      const datasetInfo: CreateUserDataFineTuneDto = {
        name: data.name,
        description: data.description,
        provider: ProviderFineTuneEnum.GOOGLE,
        trainDataset: 'application/jsonl',
        validDataset: undefined, // Google không cần validation data
      };

      // Create and upload dataset
      await createAndUpload({
        datasetInfo,
        trainJsonlData,
        validJsonlData: undefined, // Google không cần validation data
      });

      // Reset form và notify success
      reset();
      setConversations([]);
      setSelectedConversationId(null);
      setShowForm(false);

      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error('Error creating Google dataset:', error);
    }
  };

  return (
    <div className="h-full flex">
      {/* ConversationSidebarGoogle */}
      <div
        className={`transition-all duration-300 flex-shrink-0 ${isSidebarOpen ? 'w-80' : 'w-12'}`}
      >
        <ConversationSidebarGoogle
          conversations={conversations}
          selectedConversationId={selectedConversationId}
          onSelectConversation={handleSelectConversation}
          onDeleteConversation={handleDeleteConversation}
          onNewChat={handleNewChat}
          isOpen={isSidebarOpen}
          onToggleSidebar={handleToggleSidebar}
        />
      </div>

      {/* Main Content */}
      <div className="flex-1 min-w-0 h-full flex flex-col">
        {/* Form Header */}
        {showForm && (
          <div className="p-4 border-b border-gray-200 dark:border-gray-700">
            <Card>
              <form onSubmit={handleSubmit(onSubmit)}>
                <div className="grid grid-cols-1 gap-4 mb-4">
                  <FormItem name="name" label={t('Name')} helpText={errors.name?.message} required>
                    <Input
                      {...register('name')}
                      placeholder={t('Nhập tên dataset cho Google')}
                      error={errors.name?.message as string}
                      fullWidth
                    />
                  </FormItem>

                  <FormItem
                    name="description"
                    label={t('Description')}
                    helpText={errors.description?.message}
                    required
                  >
                    <Textarea
                      {...register('description')}
                      placeholder={t('Nhập mô tả dataset cho Google AI')}
                      status={errors.description?.message ? 'error' : 'default'}
                      rows={3}
                      fullWidth
                    />
                  </FormItem>
                </div>

                <div className="flex justify-between items-center">
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    {conversations.length > 0 && (
                      <span>{conversations.length} conversations • Google Provider</span>
                    )}
                  </div>

                  <div className="flex space-x-2">
                    <Button type="button" variant="outline" onClick={() => setShowForm(false)}>
                      {t('Cancel')}
                    </Button>
                    <Button
                      type="submit"
                      isLoading={isLoading}
                      disabled={conversations.length === 0}
                      className="bg-blue-600 hover:bg-blue-700"
                    >
                      {t('Tạo Dataset Google')}
                    </Button>
                  </div>
                </div>
              </form>
            </Card>
          </div>
        )}

        {/* Chat Panel */}
        <div className="flex-1 relative">
          {selectedConversation ? (
            <ChatPanel
              title={selectedConversation.title}
              messages={selectedConversation.messages}
              onAddMessage={handleAddMessage}
              onDeleteMessage={handleDeleteMessage}
              onEditMessage={handleEditMessage}
              placeholder="Nhập tin nhắn cho Google..."
            />
          ) : (
            <div className="h-full flex items-center justify-center">
              <div className="text-center">
                <div className="text-4xl mb-3">🤖</div>
                <h3 className="text-lg font-semibold mb-2 text-gray-900 dark:text-white">
                  {t('Google Dataset Builder')}
                </h3>
                <p className="text-gray-500 dark:text-gray-400 text-sm max-w-sm">
                  {t(
                    'Tạo chat mới hoặc chọn conversation để bắt đầu xây dựng dataset cho Google AI'
                  )}
                </p>
              </div>
            </div>
          )}

          {/* Create Dataset Button */}
          {conversations.length > 0 && !showForm && (
            <div className="absolute top-4 right-4">
              <Button
                onClick={() => setShowForm(true)}
                size="sm"
                className="bg-blue-600 hover:bg-blue-700"
              >
                {t('Tạo Dataset Google')} ({conversations.length})
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default DatasetFormGoogleWithSidebar;
