# User Dataset Module

Module này cung cấp các API integration cho việc quản lý dataset, model fine-tuning và các tài nguyên liên quan của user.

## 📁 Cấu trúc

```
src/modules/user-dataset/
├── user-data-fine-tune/     # Quản lý dataset fine-tune
├── user-mode-fine-tune/     # Quản lý model fine-tune  
├── user-mode-base/          # Quản lý base models
├── user-key-llm/           # Quản lý API keys
└── index.ts                # Export chính
```

## 🚀 Cách sử dụng

### Import từ module chính
```typescript
import { 
  useUserDataFineTuneList,
  useCreateUserDataFineTune,
  CreateUserDataFineTuneSchema,
  validateJsonlData 
} from '@/modules/user-dataset';
```

### Import từ sub-module
```typescript
import { 
  useUserModelFineTuneList,
  useCreateModelFineTuneFromDataset 
} from '@/modules/user-dataset/user-mode-fine-tune';
```

## 📋 Modules

### 1. user-data-fine-tune
Quản lý dataset cho fine-tuning:
- ✅ **Types**: Interfaces và enums
- ✅ **Schemas**: Zod validation 
- ✅ **Services**: API calls với named exports
- ✅ **Hooks**: React Query hooks

**Chức năng chính:**
- Tạo, cập nhật, xóa dataset
- Upload JSONL data
- Validate dữ liệu training
- Quản lý trạng thái upload

### 2. user-mode-fine-tune  
Quản lý model fine-tuning:
- ✅ **Types**: Model fine-tune interfaces
- ✅ **Schemas**: Validation schemas
- ✅ **Services**: API integration
- ✅ **Hooks**: Hooks với auto-refresh

**Chức năng chính:**
- Tạo model từ dataset
- Theo dõi tiến độ training
- Test model
- Kiểm tra compatibility
- Ước tính chi phí

### 3. user-mode-base
Quản lý base models:
- ✅ **Types**: Base model interfaces
- ✅ **Schemas**: Query validation
- ✅ **Services**: Model discovery APIs
- ✅ **Hooks**: Search và filter hooks

**Chức năng chính:**
- Duyệt danh sách base models
- So sánh models
- Đánh giá models
- Tìm kiếm và filter
- Thống kê sử dụng

### 4. user-key-llm
Quản lý API keys:
- ✅ **Types**: Key management interfaces

**Chức năng chính:**
- Quản lý API keys
- Test connectivity
- Thống kê sử dụng

## 🔧 Features

### Type Safety
- Tất cả APIs có TypeScript types
- Strict type checking
- IntelliSense support

### Validation  
- Zod schemas với error messages tiếng Việt
- Client-side validation
- Server response validation

### Caching
- React Query với proper cache management
- Automatic invalidation
- Optimistic updates

### Error Handling
- Comprehensive error handling
- User-friendly error messages
- Retry mechanisms

### Auto-refresh
- Real-time status tracking
- Automatic data refresh
- Background updates

## 📖 API Examples

### Dataset Management
```typescript
// Lấy danh sách datasets
const { data: datasets } = useUserDataFineTuneList({
  page: 1,
  limit: 10,
  status: 'COMPLETED'
});

// Tạo dataset mới
const createDataset = useCreateUserDataFineTune();
await createDataset.mutateAsync({
  name: 'My Dataset',
  description: 'Training data for chatbot',
  provider: 'OPENAI',
  trainDataset: jsonlData
});
```

### Model Fine-tuning
```typescript
// Tạo model từ dataset
const createModel = useCreateModelFineTuneFromDataset();
await createModel.mutateAsync({
  name: 'My Custom Model',
  baseModelName: 'gpt-3.5-turbo',
  datasetId: 'dataset-id'
});

// Theo dõi tiến độ
const { data: status } = useFineTuneStatus(modelId);
```

### Base Models
```typescript
// Tìm kiếm models
const { data: models } = useSearchModes('gpt', 10);

// So sánh models
const { data: comparison } = useCompareModes(['model1', 'model2']);

// Đánh giá model
const rateModel = useRateMode();
await rateModel.mutateAsync({
  id: 'model-id',
  data: { rating: 5, comment: 'Excellent!' }
});
```

## 🎯 Best Practices

### 1. Error Handling
```typescript
const { data, error, isLoading } = useUserDataFineTuneList();

if (error) {
  // Handle error appropriately
  console.error('Failed to load datasets:', error);
}
```

### 2. Loading States
```typescript
const createMutation = useCreateUserDataFineTune();

if (createMutation.isPending) {
  return <LoadingSpinner />;
}
```

### 3. Optimistic Updates
```typescript
const queryClient = useQueryClient();

// Update cache optimistically
queryClient.setQueryData(queryKey, newData);
```

## 🔄 Development

### Thêm API mới
1. Thêm types vào `types/*.types.ts`
2. Thêm validation vào `schemas/*.schemas.ts`  
3. Thêm service function vào `services/*.service.ts`
4. Thêm hooks vào `hooks/*.ts`
5. Export từ `index.ts`

### Testing
```bash
# Run type checking
npm run type-check

# Run tests
npm run test
```

## 📝 Notes

- Tất cả services sử dụng **named exports** theo pattern project
- Comments và error messages bằng tiếng Việt
- Tuân thủ coding standards của project
- Sử dụng React Query cho state management
- Type-safe với TypeScript strict mode
