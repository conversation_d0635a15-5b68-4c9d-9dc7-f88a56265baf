import React, { useState } from 'react';
import {
  Card,
  Typography,
  Button,
  Input,
  Select,
  Pagination,
  Chip,
  Loading,
  Empty,
} from '@/shared/components/common';
import { useTranslation } from 'react-i18next';
import { Search, Filter, Plus, Database, Calendar, User } from 'lucide-react';
import { useUserDataFineTuneList } from '../hooks/useUserDataFineTune';
import {
  DataFineTuneStatus,
  ProviderFineTuneEnum,
  UserDataFineTuneQueryDto,
} from '../types/user-data-fine-tune.types';

interface DatasetListProps {
  onCreateNew?: () => void;
  onSelectDataset?: (id: string) => void;
}

const DatasetList: React.FC<DatasetListProps> = ({
  onCreateNew,
  onSelectDataset,
}) => {
  const { t } = useTranslation();
  const [queryParams, setQueryParams] = useState<UserDataFineTuneQueryDto>({
    page: 1,
    limit: 12,
    search: '',
    status: undefined,
    sortBy: 'createdAt',
    sortDirection: 'DESC',
  });

  const { data, isLoading, error } = useUserDataFineTuneList(queryParams);

  // Status chip colors
  const getStatusColor = (status: DataFineTuneStatus) => {
    switch (status) {
      case DataFineTuneStatus.COMPLETED:
        return 'success';
      case DataFineTuneStatus.PROCESSING:
        return 'warning';
      case DataFineTuneStatus.FAILED:
        return 'error';
      case DataFineTuneStatus.CANCELLED:
        return 'default';
      default:
        return 'info';
    }
  };

  // Status labels
  const getStatusLabel = (status: DataFineTuneStatus) => {
    switch (status) {
      case DataFineTuneStatus.PENDING:
        return t('Đang chờ');
      case DataFineTuneStatus.PROCESSING:
        return t('Đang xử lý');
      case DataFineTuneStatus.COMPLETED:
        return t('Hoàn thành');
      case DataFineTuneStatus.FAILED:
        return t('Thất bại');
      case DataFineTuneStatus.CANCELLED:
        return t('Đã hủy');
      default:
        return status;
    }
  };

  // Provider labels
  const getProviderLabel = (provider: ProviderFineTuneEnum) => {
    switch (provider) {
      case ProviderFineTuneEnum.OPENAI:
        return 'OpenAI';
      case ProviderFineTuneEnum.ANTHROPIC:
        return 'Anthropic';
      case ProviderFineTuneEnum.GOOGLE:
        return 'Google';
      default:
        return provider;
    }
  };

  // Format date
  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('vi-VN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // Handle search
  const handleSearch = (value: string) => {
    setQueryParams(prev => ({
      ...prev,
      search: value,
      page: 1,
    }));
  };

  // Handle filter
  const handleStatusFilter = (status: DataFineTuneStatus | undefined) => {
    setQueryParams(prev => ({
      ...prev,
      status,
      page: 1,
    }));
  };

  // Handle pagination
  const handlePageChange = (page: number) => {
    setQueryParams(prev => ({
      ...prev,
      page,
    }));
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loading size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <Typography variant="body1" className="text-red-600 mb-4">
          {t('Có lỗi xảy ra khi tải dữ liệu')}
        </Typography>
        <Button
          variant="outline"
          onClick={() => window.location.reload()}
        >
          {t('Thử lại')}
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <Typography variant="h4" className="flex items-center">
            <Database className="mr-2 text-primary" size={24} />
            {t('Dataset Fine-tune')}
          </Typography>
          <Typography variant="body2" className="text-gray-600 mt-1">
            {data?.total ? `${data.total} datasets` : t('Chưa có dataset nào')}
          </Typography>
        </div>

        {onCreateNew && (
          <Button
            onClick={onCreateNew}
            className="flex items-center"
          >
            <Plus size={16} className="mr-2" />
            {t('Tạo Dataset Mới')}
          </Button>
        )}
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <Input
            placeholder={t('Tìm kiếm dataset...')}
            value={queryParams.search || ''}
            onChange={(e) => handleSearch(e.target.value)}
            prefix={<Search size={16} className="text-gray-400" />}
            className="w-full"
          />
        </div>

        <Select
          placeholder={t('Tất cả trạng thái')}
          value={queryParams.status || ''}
          onChange={(value) => handleStatusFilter(value as DataFineTuneStatus || undefined)}
          className="w-48"
        >
          <Select.Option value="">{t('Tất cả trạng thái')}</Select.Option>
          {Object.values(DataFineTuneStatus).map(status => (
            <Select.Option key={status} value={status}>
              {getStatusLabel(status)}
            </Select.Option>
          ))}
        </Select>
      </div>

      {/* Dataset Cards */}
      {data?.data && data.data.length > 0 ? (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {data.data.map((dataset) => (
              <Card
                key={dataset.id}
                className="hover:shadow-lg transition-shadow cursor-pointer"
                onClick={() => onSelectDataset?.(dataset.id)}
              >
                <div className="p-6">
                  {/* Header */}
                  <div className="flex justify-between items-start mb-4">
                    <Typography
                      variant="h6"
                      className="font-semibold text-gray-900 truncate flex-1 mr-2"
                      title={dataset.name}
                    >
                      {dataset.name}
                    </Typography>
                    
                    <Chip
                      color={getStatusColor(dataset.status)}
                      size="sm"
                      className="flex-shrink-0"
                    >
                      {getStatusLabel(dataset.status)}
                    </Chip>
                  </div>

                  {/* Description */}
                  {dataset.description && (
                    <Typography
                      variant="body2"
                      className="text-gray-600 mb-4 line-clamp-2"
                      title={dataset.description}
                    >
                      {dataset.description}
                    </Typography>
                  )}

                  {/* Provider */}
                  <div className="flex items-center mb-3">
                    <User size={14} className="text-gray-400 mr-2" />
                    <Typography variant="body2" className="text-gray-700">
                      {getProviderLabel(dataset.provider as ProviderFineTuneEnum)}
                    </Typography>
                  </div>

                  {/* Created Date */}
                  <div className="flex items-center text-gray-500">
                    <Calendar size={14} className="mr-2" />
                    <Typography variant="body2">
                      {formatDate(dataset.createdAt)}
                    </Typography>
                  </div>
                </div>
              </Card>
            ))}
          </div>

          {/* Pagination */}
          {data.totalPages > 1 && (
            <div className="flex justify-center mt-8">
              <Pagination
                current={data.page}
                total={data.total}
                pageSize={data.limit}
                onChange={handlePageChange}
                showSizeChanger={false}
                showQuickJumper
                showTotal={(total, range) =>
                  `${range[0]}-${range[1]} của ${total} datasets`
                }
              />
            </div>
          )}
        </>
      ) : (
        <Empty
          description={
            queryParams.search || queryParams.status
              ? t('Không tìm thấy dataset nào phù hợp')
              : t('Chưa có dataset nào. Tạo dataset đầu tiên của bạn!')
          }
          action={
            onCreateNew && !queryParams.search && !queryParams.status ? (
              <Button onClick={onCreateNew}>
                {t('Tạo Dataset Đầu Tiên')}
              </Button>
            ) : undefined
          }
        />
      )}
    </div>
  );
};

export default DatasetList;
