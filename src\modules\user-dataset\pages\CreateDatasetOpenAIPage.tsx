import React from 'react';

import { useNavigate } from 'react-router-dom';

import DatasetForm from '../components/DatasetForm';

/**
 * Page để tạo dataset cho OpenAI
 * Sử dụng DatasetForm với đầy đủ components (có ConversationSidebar)
 */
const CreateDatasetOpenAIPage: React.FC = () => {
  const navigate = useNavigate();

  const handleSuccess = () => {
    // Navigate back to dataset list
    navigate('/user-dataset/data-fine-tune');
  };

  return (
    <div className="h-full flex flex-col">
      {/* Main Content */}
      <div className="flex-1 overflow-hidden">
        <DatasetForm onSuccess={handleSuccess} />
      </div>
    </div>
  );
};

export default CreateDatasetOpenAIPage;
