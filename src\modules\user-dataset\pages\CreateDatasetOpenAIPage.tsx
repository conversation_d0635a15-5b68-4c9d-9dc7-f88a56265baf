import React, { useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/shared/components/common';
import { FileText, BarChart3 } from 'lucide-react';
import DatasetForm from '../components/DatasetForm';
import ValidationDataForm from '../components/ValidationDataForm';
import { ImportedConversation } from '../../model-training/types/dataset.types';

// Constants cho localStorage keys
const STORAGE_KEYS = {
  TRAINING_CONVERSATIONS: 'openai_training_conversations',
  VALIDATION_CONVERSATIONS: 'openai_validation_conversations',
  ACTIVE_TAB: 'openai_active_tab',
};

/**
 * Page để tạo dataset cho OpenAI
 * Có 2 tabs: Training Data và Validation Data
 * Sử dụng DatasetForm với đầy đủ components (có ConversationSidebar)
 * Lưu conversations vào localStorage để không mất khi reload
 */
const CreateDatasetOpenAIPage: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  // State cho tab selection với localStorage
  const [activeTab, setActiveTab] = useState<'training' | 'validation'>(() => {
    const saved = localStorage.getItem(STORAGE_KEYS.ACTIVE_TAB);
    return (saved as 'training' | 'validation') || 'training';
  });

  // State để control form hiển thị
  const [showTrainingForm, setShowTrainingForm] = useState(false);
  const [showValidationForm, setShowValidationForm] = useState(false);

  // State cho conversations với localStorage
  const [trainingConversations, setTrainingConversations] = useState<ImportedConversation[]>(() => {
    const saved = localStorage.getItem(STORAGE_KEYS.TRAINING_CONVERSATIONS);
    return saved ? JSON.parse(saved) : [];
  });

  const [validationConversations, setValidationConversations] = useState<ImportedConversation[]>(
    () => {
      const saved = localStorage.getItem(STORAGE_KEYS.VALIDATION_CONVERSATIONS);
      return saved ? JSON.parse(saved) : [];
    }
  );

  // Save to localStorage khi conversations thay đổi
  useEffect(() => {
    localStorage.setItem(
      STORAGE_KEYS.TRAINING_CONVERSATIONS,
      JSON.stringify(trainingConversations)
    );
  }, [trainingConversations]);

  useEffect(() => {
    localStorage.setItem(
      STORAGE_KEYS.VALIDATION_CONVERSATIONS,
      JSON.stringify(validationConversations)
    );
  }, [validationConversations]);

  useEffect(() => {
    localStorage.setItem(STORAGE_KEYS.ACTIVE_TAB, activeTab);
  }, [activeTab]);

  // Handle conversations change
  const handleTrainingConversationsChange = useCallback((conversations: ImportedConversation[]) => {
    setTrainingConversations(conversations);
  }, []);

  const handleValidationConversationsChange = useCallback(
    (conversations: ImportedConversation[]) => {
      setValidationConversations(conversations);
    },
    []
  );

  // Clear localStorage
  const clearStorage = useCallback(() => {
    localStorage.removeItem(STORAGE_KEYS.TRAINING_CONVERSATIONS);
    localStorage.removeItem(STORAGE_KEYS.VALIDATION_CONVERSATIONS);
    localStorage.removeItem(STORAGE_KEYS.ACTIVE_TAB);
  }, []);

  const handleSuccess = () => {
    // Clear localStorage khi tạo dataset thành công
    clearStorage();
    // Navigate back to dataset list
    navigate('/user-dataset/data-fine-tune');
  };

  return (
    <div className="h-screen flex flex-col overflow-hidden">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 flex-shrink-0">
        <div className="flex items-center space-x-4">
          <div className="text-sm text-gray-600 dark:text-gray-400">
            <span className="mr-4">Training: {trainingConversations.length} conversations</span>
            <span>Validation: {validationConversations.length} conversations</span>
          </div>

          {/* Nút Tạo Dataset chính */}
          {(trainingConversations.length > 0 || validationConversations.length > 0) && (
            <Button
              onClick={() => {
                // Trigger form hiển thị trong component con
                if (activeTab === 'training') {
                  setShowTrainingForm(true);
                } else {
                  setShowValidationForm(true);
                }
              }}
              size="sm"
              className="bg-green-600 hover:bg-green-700 text-white"
            >
              {t('Tạo Dataset')} (
              {activeTab === 'training'
                ? trainingConversations.length
                : validationConversations.length}
              )
            </Button>
          )}

          <div className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400">
            <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
            OpenAI Provider
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900 flex-shrink-0">
        <div className="flex space-x-1 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg">
          <Button
            variant={activeTab === 'training' ? 'primary' : 'ghost'}
            size="sm"
            onClick={() => setActiveTab('training')}
            className="flex items-center"
          >
            <FileText size={16} className="mr-2" />
            {t('Training Data')}
          </Button>
          <Button
            variant={activeTab === 'validation' ? 'primary' : 'ghost'}
            size="sm"
            onClick={() => setActiveTab('validation')}
            className="flex items-center"
          >
            <BarChart3 size={16} className="mr-2" />
            {t('Validation Data')}
          </Button>
        </div>

        <div className="text-sm text-gray-600 dark:text-gray-400">
          {activeTab === 'training' && t('Tạo dữ liệu huấn luyện cho OpenAI')}
          {activeTab === 'validation' && t('Tạo dữ liệu validation cho OpenAI')}
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 min-h-0 overflow-hidden">
        {activeTab === 'training' && (
          <DatasetForm
            onSuccess={handleSuccess}
            onConversationsChange={handleTrainingConversationsChange}
            initialConversations={trainingConversations}
            showForm={showTrainingForm}
            onShowFormChange={setShowTrainingForm}
          />
        )}

        {activeTab === 'validation' && (
          <ValidationDataForm
            onSuccess={handleSuccess}
            onConversationsChange={handleValidationConversationsChange}
            initialConversations={validationConversations}
            showForm={showValidationForm}
            onShowFormChange={setShowValidationForm}
          />
        )}
      </div>
    </div>
  );
};

export default CreateDatasetOpenAIPage;
