import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/shared/components/common';
import { FileText, BarChart3 } from 'lucide-react';
import DatasetForm from '../components/DatasetForm';
import ValidationDataForm from '../components/ValidationDataForm';

/**
 * Page để tạo dataset cho OpenAI
 * Có 2 tabs: Training Data và Validation Data
 * Sử dụng DatasetForm với đầy đủ components (có ConversationSidebar)
 */
const CreateDatasetOpenAIPage: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  // State cho tab selection
  const [activeTab, setActiveTab] = useState<'training' | 'validation'>('training');

  const handleSuccess = () => {
    // Navigate back to dataset list
    navigate('/user-dataset/data-fine-tune');
  };

  return (
    <div className="h-screen flex flex-col">
      {/* Tab Navigation */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900">
        <div className="flex space-x-1 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg">
          <Button
            variant={activeTab === 'training' ? 'primary' : 'ghost'}
            size="sm"
            onClick={() => setActiveTab('training')}
            className="flex items-center"
          >
            <FileText size={16} className="mr-2" />
            {t('Training Data')}
          </Button>
          <Button
            variant={activeTab === 'validation' ? 'primary' : 'ghost'}
            size="sm"
            onClick={() => setActiveTab('validation')}
            className="flex items-center"
          >
            <BarChart3 size={16} className="mr-2" />
            {t('Validation Data')}
          </Button>
        </div>

        <div className="text-sm text-gray-600 dark:text-gray-400">
          {activeTab === 'training' && t('Tạo dữ liệu huấn luyện cho OpenAI')}
          {activeTab === 'validation' && t('Tạo dữ liệu validation cho OpenAI')}
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-hidden">
        {activeTab === 'training' && <DatasetForm onSuccess={handleSuccess} />}

        {activeTab === 'validation' && <ValidationDataForm onSuccess={handleSuccess} />}
      </div>
    </div>
  );
};

export default CreateDatasetOpenAIPage;
