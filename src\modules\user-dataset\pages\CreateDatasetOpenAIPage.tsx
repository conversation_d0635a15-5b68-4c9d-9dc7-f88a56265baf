import React, { useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { Button, Input, FormItem, Textarea } from '@/shared/components/common';
import { ArrowLeft, FileText, BarChart3 } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import DatasetForm from '../components/DatasetForm';
import ValidationDataForm from '../components/ValidationDataForm';
import { useCreateAndUploadDataset } from '../user-data-fine-tune/hooks/useUserDataFineTune';
import {
  CreateUserDataFineTuneDto,
  ProviderFineTuneEnum,
  ImportedConversation,
} from '../user-data-fine-tune/types/user-data-fine-tune.types';
import {
  convertConversationsToJsonl,
  validateJsonlData,
} from '../user-data-fine-tune/services/user-data-fine-tune.service';

// Constants cho localStorage keys
const STORAGE_KEYS = {
  TRAINING_CONVERSATIONS: 'openai_training_conversations',
  VALIDATION_CONVERSATIONS: 'openai_validation_conversations',
  ACTIVE_TAB: 'openai_active_tab',
};

// Schema validation cho unified dataset form
const UnifiedDatasetFormSchema = z.object({
  name: z.string().min(1, 'Dataset name is required'),
  description: z.string().min(1, 'Dataset description is required'),
});

type UnifiedDatasetFormData = z.infer<typeof UnifiedDatasetFormSchema>;

/**
 * Page để tạo dataset cho OpenAI
 * Có 2 tabs: Training Data và Validation Data
 * Tạo 1 nút chung để gộp cả training và validation data
 * Lưu conversations vào localStorage để không mất khi reload
 */
const CreateDatasetOpenAIPage: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { createAndUpload, isLoading } = useCreateAndUploadDataset();

  // State cho tab selection với localStorage
  const [activeTab, setActiveTab] = useState<'training' | 'validation'>(() => {
    const saved = localStorage.getItem(STORAGE_KEYS.ACTIVE_TAB);
    return (saved as 'training' | 'validation') || 'training';
  });

  // State để control unified form hiển thị
  const [showUnifiedForm, setShowUnifiedForm] = useState(false);

  // State cho conversations với localStorage
  const [trainingConversations, setTrainingConversations] = useState<ImportedConversation[]>(() => {
    const saved = localStorage.getItem(STORAGE_KEYS.TRAINING_CONVERSATIONS);
    return saved ? JSON.parse(saved) : [];
  });

  const [validationConversations, setValidationConversations] = useState<ImportedConversation[]>(
    () => {
      const saved = localStorage.getItem(STORAGE_KEYS.VALIDATION_CONVERSATIONS);
      return saved ? JSON.parse(saved) : [];
    }
  );

  // Khởi tạo form với React Hook Form và Zod
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<UnifiedDatasetFormData>({
    resolver: zodResolver(UnifiedDatasetFormSchema),
    defaultValues: {
      name: '',
      description: '',
    },
  });

  // Save to localStorage khi conversations thay đổi
  useEffect(() => {
    localStorage.setItem(
      STORAGE_KEYS.TRAINING_CONVERSATIONS,
      JSON.stringify(trainingConversations)
    );
  }, [trainingConversations]);

  useEffect(() => {
    localStorage.setItem(
      STORAGE_KEYS.VALIDATION_CONVERSATIONS,
      JSON.stringify(validationConversations)
    );
  }, [validationConversations]);

  useEffect(() => {
    localStorage.setItem(STORAGE_KEYS.ACTIVE_TAB, activeTab);
  }, [activeTab]);

  // Handle conversations change
  const handleTrainingConversationsChange = useCallback((conversations: ImportedConversation[]) => {
    setTrainingConversations(conversations);
  }, []);

  const handleValidationConversationsChange = useCallback(
    (conversations: ImportedConversation[]) => {
      setValidationConversations(conversations);
    },
    []
  );

  // Clear localStorage
  const clearStorage = useCallback(() => {
    localStorage.removeItem(STORAGE_KEYS.TRAINING_CONVERSATIONS);
    localStorage.removeItem(STORAGE_KEYS.VALIDATION_CONVERSATIONS);
    localStorage.removeItem(STORAGE_KEYS.ACTIVE_TAB);
  }, []);

  // Xử lý submit form tạo dataset
  const onSubmit = async (data: UnifiedDatasetFormData) => {
    try {
      // Convert conversations to JSONL format
      const trainJsonlData =
        trainingConversations.length > 0 ? convertConversationsToJsonl(trainingConversations) : '';

      const validJsonlData =
        validationConversations.length > 0
          ? convertConversationsToJsonl(validationConversations)
          : undefined;

      // Validate training data
      if (trainJsonlData) {
        const trainValidation = validateJsonlData(trainJsonlData);
        if (!trainValidation.isValid) {
          console.error('Training data validation failed:', trainValidation.errors);
          return;
        }
      }

      // Validate validation data if exists
      if (validJsonlData) {
        const validValidation = validateJsonlData(validJsonlData);
        if (!validValidation.isValid) {
          console.error('Validation data validation failed:', validValidation.errors);
          return;
        }
      }

      // Create dataset info for OpenAI
      const datasetInfo: CreateUserDataFineTuneDto = {
        name: data.name,
        description: data.description,
        provider: ProviderFineTuneEnum.OPENAI,
        trainDataset: 'application/jsonl',
        validDataset: validJsonlData ? 'application/jsonl' : undefined,
      };

      // Create and upload dataset
      await createAndUpload({
        datasetInfo,
        trainJsonlData:
          trainJsonlData || '{"messages":[{"role":"system","content":"Empty training data"}]}',
        validJsonlData,
      });

      // Reset form và clear data
      reset();
      clearStorage();
      setShowUnifiedForm(false);

      // Navigate back to dataset list
      navigate('/user-dataset/data-fine-tune');
    } catch (error) {
      console.error('Error creating OpenAI dataset:', error);
    }
  };

  const handleBack = () => {
    navigate('/user-dataset/data-fine-tune');
  };

  // Get current conversation counts from state
  const currentTrainingCount = trainingConversations.length;
  const currentValidationCount = validationConversations.length;

  return (
    <div className="h-screen flex flex-col overflow-hidden">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 flex-shrink-0">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" onClick={handleBack} className="flex items-center">
            <ArrowLeft size={16} className="mr-2" />
            {t('Quay lại')}
          </Button>
        </div>

        <div className="flex items-center space-x-4">
          <div className="text-sm text-gray-600 dark:text-gray-400">
            <span className="mr-4">Training: {currentTrainingCount} conversations</span>
            <span>Validation: {currentValidationCount} conversations</span>
          </div>

          {/* Nút Tạo Dataset chính */}
          {(currentTrainingCount > 0 || currentValidationCount > 0) && (
            <Button
              onClick={() => setShowUnifiedForm(true)}
              size="sm"
              className="bg-green-600 hover:bg-green-700 text-white"
            >
              {t('Tạo Dataset')} ({currentTrainingCount + currentValidationCount})
            </Button>
          )}

          <div className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400">
            <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
            OpenAI Provider
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900 flex-shrink-0">
        <div className="flex space-x-1 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg">
          <Button
            variant={activeTab === 'training' ? 'primary' : 'ghost'}
            size="sm"
            onClick={() => setActiveTab('training')}
            className="flex items-center"
          >
            <FileText size={16} className="mr-2" />
            {t('Training Data')}
          </Button>
          <Button
            variant={activeTab === 'validation' ? 'primary' : 'ghost'}
            size="sm"
            onClick={() => setActiveTab('validation')}
            className="flex items-center"
          >
            <BarChart3 size={16} className="mr-2" />
            {t('Validation Data')}
          </Button>
        </div>

        <div className="text-sm text-gray-600 dark:text-gray-400">
          {activeTab === 'training' && t('Tạo dữ liệu huấn luyện cho OpenAI')}
          {activeTab === 'validation' && t('Tạo dữ liệu validation cho OpenAI')}
        </div>
      </div>

      {/* Unified Form Modal */}
      {showUnifiedForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4">
            <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">
              {t('Tạo Dataset OpenAI')}
            </h3>

            <form onSubmit={handleSubmit(onSubmit)}>
              <div className="space-y-4">
                <FormItem name="name" label={t('Name')} helpText={errors.name?.message} required>
                  <Input
                    {...register('name')}
                    placeholder={t('Nhập tên dataset')}
                    error={errors.name?.message as string}
                    fullWidth
                  />
                </FormItem>

                <FormItem
                  name="description"
                  label={t('Description')}
                  helpText={errors.description?.message}
                  required
                >
                  <Textarea
                    {...register('description')}
                    placeholder={t('Nhập mô tả dataset')}
                    status={errors.description?.message ? 'error' : 'default'}
                    rows={3}
                    fullWidth
                  />
                </FormItem>

                <div className="text-sm text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-700 p-3 rounded">
                  <div className="font-medium mb-1">{t('Dữ liệu sẽ được sử dụng:')}</div>
                  <div>• Training: {currentTrainingCount} conversations</div>
                  <div>• Validation: {currentValidationCount} conversations</div>
                </div>
              </div>

              <div className="flex justify-end space-x-3 mt-6">
                <Button type="button" variant="outline" onClick={() => setShowUnifiedForm(false)}>
                  {t('Cancel')}
                </Button>
                <Button
                  type="submit"
                  isLoading={isLoading}
                  disabled={currentTrainingCount === 0 && currentValidationCount === 0}
                  className="bg-green-600 hover:bg-green-700"
                >
                  {t('Tạo Dataset')}
                </Button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Main Content */}
      <div className="flex-1 min-h-0 overflow-hidden">
        {activeTab === 'training' && (
          <DatasetForm onConversationsChange={handleTrainingConversationsChange} />
        )}

        {activeTab === 'validation' && (
          <ValidationDataForm onConversationsChange={handleValidationConversationsChange} />
        )}
      </div>
    </div>
  );
};

export default CreateDatasetOpenAIPage;
