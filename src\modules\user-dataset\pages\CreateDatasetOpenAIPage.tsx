import React, { useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { Button, Input, FormItem, Textarea } from '@/shared/components/common';
import { ArrowLeft, FileText, BarChart3 } from 'lucide-react';
// Removed React Hook Form - using simple useState instead
import DatasetForm from '../components/DatasetForm';
import ValidationDataForm from '../components/ValidationDataForm';

import { useCreateAndUploadDataset } from '../user-data-fine-tune/hooks/useUserDataFineTune';
import {
  ProviderFineTuneEnum,
  ImportedConversation,
} from '../user-data-fine-tune/types/user-data-fine-tune.types';
import { convertConversationsToMessagesArray } from '../types/jsonl.types';

// Constants cho localStorage keys
const STORAGE_KEYS = {
  TRAINING_CONVERSATIONS: 'openai_training_conversations',
  VALIDATION_CONVERSATIONS: 'openai_validation_conversations',
  ACTIVE_TAB: 'openai_active_tab',
};

// Simple form data interface
interface UnifiedDatasetFormData {
  name: string;
  description: string;
}

/**
 * Page để tạo dataset cho OpenAI
 * Có 2 tabs: Training Data và Validation Data
 * Tạo 1 nút chung để gộp cả training và validation data
 * Lưu conversations vào localStorage để không mất khi reload
 */
const CreateDatasetOpenAIPage: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { createAndUpload } = useCreateAndUploadDataset();

  // State cho tab selection với localStorage
  const [activeTab, setActiveTab] = useState<'training' | 'validation'>(() => {
    const saved = localStorage.getItem(STORAGE_KEYS.ACTIVE_TAB);
    return (saved as 'training' | 'validation') || 'training';
  });

  // State để control unified form hiển thị
  const [showUnifiedForm, setShowUnifiedForm] = useState(false);

  // State cho conversations với localStorage
  const [trainingConversations, setTrainingConversations] = useState<ImportedConversation[]>(() => {
    const saved = localStorage.getItem(STORAGE_KEYS.TRAINING_CONVERSATIONS);
    return saved ? JSON.parse(saved) : [];
  });

  const [validationConversations, setValidationConversations] = useState<ImportedConversation[]>(
    () => {
      const saved = localStorage.getItem(STORAGE_KEYS.VALIDATION_CONVERSATIONS);
      return saved ? JSON.parse(saved) : [];
    }
  );

  // Simple form state với useState
  const [formData, setFormData] = useState<UnifiedDatasetFormData>({
    name: '',
    description: '',
  });
  const [formErrors, setFormErrors] = useState<Partial<UnifiedDatasetFormData>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Save to localStorage khi conversations thay đổi
  useEffect(() => {
    localStorage.setItem(
      STORAGE_KEYS.TRAINING_CONVERSATIONS,
      JSON.stringify(trainingConversations)
    );
  }, [trainingConversations]);

  useEffect(() => {
    localStorage.setItem(
      STORAGE_KEYS.VALIDATION_CONVERSATIONS,
      JSON.stringify(validationConversations)
    );
  }, [validationConversations]);

  useEffect(() => {
    localStorage.setItem(STORAGE_KEYS.ACTIVE_TAB, activeTab);
  }, [activeTab]);

  // Handle conversations change
  const handleTrainingConversationsChange = useCallback((conversations: ImportedConversation[]) => {
    console.log(
      '🎯 [CreateDatasetOpenAIPage] Training conversations changed:',
      conversations.length
    );
    setTrainingConversations(conversations);
  }, []);

  const handleValidationConversationsChange = useCallback(
    (conversations: ImportedConversation[]) => {
      console.log(
        '✅ [CreateDatasetOpenAIPage] Validation conversations changed:',
        conversations.length
      );
      setValidationConversations(conversations);
    },
    []
  );

  // Clear localStorage
  const clearStorage = useCallback(() => {
    localStorage.removeItem(STORAGE_KEYS.TRAINING_CONVERSATIONS);
    localStorage.removeItem(STORAGE_KEYS.VALIDATION_CONVERSATIONS);
    localStorage.removeItem(STORAGE_KEYS.ACTIVE_TAB);
  }, []);

  // Simple form validation
  const validateForm = (): boolean => {
    const errors: Partial<UnifiedDatasetFormData> = {};

    if (!formData.name.trim()) {
      errors.name = 'Dataset name is required';
    }

    if (!formData.description.trim()) {
      errors.description = 'Dataset description is required';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Xử lý submit form tạo dataset
  const handleFormSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    console.log('�🔥🔥 [CreateDatasetOpenAIPage] handleFormSubmit CALLED! 🔥🔥🔥');
    console.log('�🚀 [CreateDatasetOpenAIPage] Starting dataset creation...');
    console.log('🚀 [CreateDatasetOpenAIPage] Form data:', formData);
    console.log(
      '🚀 [CreateDatasetOpenAIPage] Training conversations:',
      trainingConversations.length
    );
    console.log(
      '🚀 [CreateDatasetOpenAIPage] Validation conversations:',
      validationConversations.length
    );

    // Validate form
    if (!validateForm()) {
      console.log('🚀 [CreateDatasetOpenAIPage] Form validation failed:', formErrors);
      return;
    }

    setIsSubmitting(true);

    try {
      // Convert conversations to Messages array format
      const trainDataArray = convertConversationsToMessagesArray(trainingConversations);
      const validDataArray =
        validationConversations.length > 0
          ? convertConversationsToMessagesArray(validationConversations)
          : [];

      console.log('🚀 [CreateDatasetOpenAIPage] Data arrays prepared:', {
        trainDataArray: trainDataArray.length,
        validDataArray: validDataArray.length,
      });

      // Convert arrays to JSONL strings
      const trainJsonlString = trainDataArray.map(item => JSON.stringify(item)).join('\n');

      const validJsonlString =
        validDataArray.length > 0
          ? validDataArray.map(item => JSON.stringify(item)).join('\n')
          : '';

      console.log('🚀 [CreateDatasetOpenAIPage] JSONL strings prepared:', {
        trainJsonlLength: trainJsonlString.length,
        validJsonlLength: validJsonlString.length,
      });

      // Create File objects with application/jsonl type
      const trainFile = new File([trainJsonlString], 'train_data.jsonl', {
        type: 'application/jsonl',
      });

      const validFile = validJsonlString
        ? new File([validJsonlString], 'valid_data.jsonl', {
            type: 'application/jsonl',
          })
        : undefined;

      console.log('🚀 [CreateDatasetOpenAIPage] File objects created:', {
        trainFile: {
          name: trainFile.name,
          type: trainFile.type,
          size: trainFile.size,
        },
        validFile: validFile
          ? {
              name: validFile.name,
              type: validFile.type,
              size: validFile.size,
            }
          : 'No validation file',
      });

      // Create dataset info for OpenAI - Use File objects with application/jsonl type
      const datasetInfo: any = {
        name: formData.name,
        description: formData.description,
        provider: ProviderFineTuneEnum.OPENAI,
        trainDataset: trainFile, // ✅ Send File object with application/jsonl type
        validDataset: validFile, // ✅ Send File object with application/jsonl type (or undefined)
      };

      console.log('🚀 [CreateDatasetOpenAIPage] Calling createAndUpload with File objects...');

      // Create and upload dataset with File objects
      await createAndUpload({
        datasetInfo,
        trainJsonlData: '', // Not used when sending File objects
        validJsonlData: undefined, // Not used when sending File objects
      });

      // Reset form và clear data
      setFormData({ name: '', description: '' });
      setFormErrors({});
      clearStorage();
      setShowUnifiedForm(false);

      // Navigate back to dataset list
      navigate('/user-dataset/data-fine-tune');
    } catch (error) {
      console.error('Error creating OpenAI dataset:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleBack = () => {
    navigate('/user-dataset/data-fine-tune');
  };

  // Get current conversation counts from state
  const currentTrainingCount = trainingConversations.length;
  const currentValidationCount = validationConversations.length;

  // Debug logging
  console.log('🔍 [CreateDatasetOpenAIPage] Current counts:', {
    trainingCount: currentTrainingCount,
    validationCount: currentValidationCount,
    showButton: currentTrainingCount > 0 || currentValidationCount > 0,
    activeTab,
  });

  // Debug form state
  console.log('🔍 [CreateDatasetOpenAIPage] Form state:', {
    formData,
    formErrors,
    isSubmitting,
    showUnifiedForm,
    isValid: Object.keys(formErrors).length === 0,
  });

  return (
    <div className="h-screen flex flex-col overflow-hidden">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 flex-shrink-0">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" onClick={handleBack} className="flex items-center">
            <ArrowLeft size={16} className="mr-2" />
            {t('Quay lại')}
          </Button>
        </div>

        <div className="flex items-center space-x-4">
          <div className="text-sm text-gray-600 dark:text-gray-400">
            <span className="mr-4">Training: {currentTrainingCount} conversations</span>
            <span>Validation: {currentValidationCount} conversations</span>
          </div>

          {/* Nút Tạo Dataset chính */}
          {(currentTrainingCount > 0 || currentValidationCount > 0) && (
            <Button
              onClick={() => setShowUnifiedForm(true)}
              size="sm"
              className="bg-green-600 hover:bg-green-700 text-white"
            >
              {t('Tạo Dataset')} ({currentTrainingCount + currentValidationCount})
            </Button>
          )}

          <div className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400">
            <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
            OpenAI Provider
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900 flex-shrink-0">
        <div className="flex space-x-1 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg">
          <Button
            variant={activeTab === 'training' ? 'primary' : 'ghost'}
            size="sm"
            onClick={() => setActiveTab('training')}
            className="flex items-center"
          >
            <FileText size={16} className="mr-2" />
            {t('Training Data')}
          </Button>
          <Button
            variant={activeTab === 'validation' ? 'primary' : 'ghost'}
            size="sm"
            onClick={() => setActiveTab('validation')}
            className="flex items-center"
          >
            <BarChart3 size={16} className="mr-2" />
            {t('Validation Data')}
          </Button>
        </div>

        <div className="text-sm text-gray-600 dark:text-gray-400">
          {activeTab === 'training' && t('Tạo dữ liệu huấn luyện cho OpenAI')}
          {activeTab === 'validation' && t('Tạo dữ liệu validation cho OpenAI')}
        </div>
      </div>

      {/* Unified Form Modal */}
      {showUnifiedForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4">
            <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">
              {t('Tạo Dataset OpenAI')}
            </h3>

            <form
              onSubmit={handleFormSubmit}
              onSubmitCapture={() =>
                console.log('🔥 [CreateDatasetOpenAIPage] Form submit captured!')
              }
            >
              <div className="space-y-4">
                <FormItem name="name" label={t('Name')} helpText={formErrors.name} required>
                  <Input
                    value={formData.name}
                    onChange={e => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    placeholder={t('Nhập tên dataset')}
                    error={formErrors.name}
                    fullWidth
                  />
                </FormItem>

                <FormItem
                  name="description"
                  label={t('Description')}
                  helpText={formErrors.description}
                  required
                >
                  <Textarea
                    value={formData.description}
                    onChange={e => setFormData(prev => ({ ...prev, description: e.target.value }))}
                    placeholder={t('Nhập mô tả dataset')}
                    status={formErrors.description ? 'error' : 'default'}
                    rows={3}
                    fullWidth
                  />
                </FormItem>

                <div className="text-sm text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-700 p-3 rounded">
                  <div className="font-medium mb-1">{t('Dữ liệu sẽ được sử dụng:')}</div>
                  <div>• Training: {currentTrainingCount} conversations</div>
                  <div>• Validation: {currentValidationCount} conversations</div>
                </div>
              </div>

              <div className="flex justify-end space-x-3 mt-6">
                <Button type="button" variant="outline" onClick={() => setShowUnifiedForm(false)}>
                  {t('Cancel')}
                </Button>
                <Button
                  type="submit"
                  isLoading={isSubmitting}
                  disabled={currentTrainingCount === 0 && currentValidationCount === 0}
                  className="bg-green-600 hover:bg-green-700"
                  onClick={() => console.log('🔥 [CreateDatasetOpenAIPage] Submit button clicked!')}
                >
                  {t('Tạo Dataset')}
                </Button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Main Content */}
      <div className="flex-1 min-h-0 overflow-hidden">
        {activeTab === 'training' && (
          <DatasetForm onConversationsChange={handleTrainingConversationsChange} />
        )}

        {activeTab === 'validation' && (
          <ValidationDataForm onConversationsChange={handleValidationConversationsChange} />
        )}
      </div>
    </div>
  );
};

export default CreateDatasetOpenAIPage;
