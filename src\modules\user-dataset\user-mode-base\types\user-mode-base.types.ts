/**
 * Enums cho User Mode Base
 */
export enum ModeBaseProvider {
  OPENAI = 'OPENAI',
  ANTHROPIC = 'ANTHROPIC',
  GOOGLE = 'GOOGLE',
  HUGGINGFACE = 'HUGGINGFACE',
  CUSTOM = 'CUSTOM',
}

export enum ModeBaseStatus {
  AVAILABLE = 'AVAILABLE',
  DEPRECATED = 'DEPRECATED',
  BETA = 'BETA',
  MAINTENANCE = 'MAINTENANCE',
}

export enum ModeBaseType {
  CHAT = 'CHAT',
  COMPLETION = 'COMPLETION',
  EMBEDDING = 'EMBEDDING',
  IMAGE = 'IMAGE',
  AUDIO = 'AUDIO',
  MULTIMODAL = 'MULTIMODAL',
}

export enum UserModeBaseSortBy {
  NAME = 'name',
  PROVIDER = 'provider',
  CREATED_AT = 'createdAt',
  POPULARITY = 'popularity',
}

/**
 * Response cho mode base
 */
export interface UserModeBaseResponseDto {
  /**
   * ID của mode
   */
  id: string;

  /**
   * Tên mode
   */
  name: string;

  /**
   * Tên hiển thị
   */
  displayName: string;

  /**
   * Mô tả mode
   */
  description: string;

  /**
   * Nhà cung cấp
   */
  provider: ModeBaseProvider;

  /**
   * Loại mode
   */
  type: ModeBaseType;

  /**
   * Trạng thái
   */
  status: ModeBaseStatus;

  /**
   * Số token tối đa
   */
  maxTokens: number;

  /**
   * Chi phí per token (input)
   */
  costPerInputToken: number;

  /**
   * Chi phí per token (output)
   */
  costPerOutputToken: number;

  /**
   * Đơn vị tiền tệ
   */
  currency: string;

  /**
   * Có hỗ trợ fine-tuning không
   */
  supportsFineTuning: boolean;

  /**
   * Có hỗ trợ function calling không
   */
  supportsFunctionCalling: boolean;

  /**
   * Có hỗ trợ vision không
   */
  supportsVision: boolean;

  /**
   * Thời gian tạo
   */
  createdAt: number;

  /**
   * Thời gian cập nhật
   */
  updatedAt: number;

  /**
   * Số lần sử dụng
   */
  usageCount: number;

  /**
   * Đánh giá trung bình
   */
  averageRating: number;
}

/**
 * Response chi tiết cho mode base
 */
export interface UserModeBaseDetailResponseDto extends UserModeBaseResponseDto {
  /**
   * Thông số kỹ thuật chi tiết
   */
  specifications: {
    architecture: string;
    trainingData: string;
    languages: string[];
    capabilities: string[];
    limitations: string[];
  };

  /**
   * Cấu hình mặc định
   */
  defaultConfig: {
    temperature: number;
    maxTokens: number;
    topP: number;
    frequencyPenalty: number;
    presencePenalty: number;
  };

  /**
   * Ví dụ sử dụng
   */
  examples: Array<{
    title: string;
    description: string;
    input: string;
    output: string;
    config?: Record<string, unknown>;
  }>;

  /**
   * Thống kê hiệu suất
   */
  performanceStats: {
    averageResponseTime: number;
    successRate: number;
    errorRate: number;
    throughput: number;
  };

  /**
   * Changelog
   */
  changelog: Array<{
    version: string;
    date: string;
    changes: string[];
  }>;
}

/**
 * Query DTO cho mode base
 */
export interface UserModeBaseQueryDto {
  /**
   * Số trang (bắt đầu từ 1)
   */
  page?: number;

  /**
   * Số lượng item trên mỗi trang
   */
  limit?: number;

  /**
   * Từ khóa tìm kiếm
   */
  search?: string;

  /**
   * Tìm kiếm theo nhà cung cấp
   */
  provider?: ModeBaseProvider;

  /**
   * Tìm kiếm theo loại mode
   */
  type?: ModeBaseType;

  /**
   * Tìm kiếm theo trạng thái
   */
  status?: ModeBaseStatus;

  /**
   * Chỉ hiển thị modes hỗ trợ fine-tuning
   */
  supportsFineTuning?: boolean;

  /**
   * Chỉ hiển thị modes hỗ trợ function calling
   */
  supportsFunctionCalling?: boolean;

  /**
   * Chỉ hiển thị modes hỗ trợ vision
   */
  supportsVision?: boolean;

  /**
   * Trường sắp xếp
   */
  sortBy?: UserModeBaseSortBy;

  /**
   * Hướng sắp xếp
   */
  sortDirection?: 'ASC' | 'DESC';
}

/**
 * DTO cho việc so sánh modes
 */
export interface CompareModesDto {
  /**
   * Danh sách ID modes cần so sánh
   */
  modeIds: string[];
}

/**
 * Response cho việc so sánh modes
 */
export interface CompareModesResponseDto {
  /**
   * Thông tin các modes
   */
  modes: UserModeBaseDetailResponseDto[];

  /**
   * Bảng so sánh
   */
  comparison: {
    features: Array<{
      name: string;
      values: Array<{
        modeId: string;
        value: string | number | boolean;
        score?: number;
      }>;
    }>;
    
    performance: Array<{
      metric: string;
      values: Array<{
        modeId: string;
        value: number;
        unit: string;
      }>;
    }>;

    cost: Array<{
      scenario: string;
      values: Array<{
        modeId: string;
        cost: number;
        currency: string;
      }>;
    }>;
  };

  /**
   * Khuyến nghị
   */
  recommendations: Array<{
    useCase: string;
    recommendedModeId: string;
    reason: string;
    score: number;
  }>;
}

/**
 * DTO cho việc đánh giá mode
 */
export interface RateModeDto {
  /**
   * Điểm đánh giá (1-5)
   */
  rating: number;

  /**
   * Nhận xét
   */
  comment?: string;

  /**
   * Use case
   */
  useCase?: string;
}

/**
 * Response cho việc đánh giá mode
 */
export interface RateModeResponseDto {
  /**
   * Thành công
   */
  success: boolean;

  /**
   * Thông báo
   */
  message: string;

  /**
   * Điểm đánh giá mới
   */
  newAverageRating: number;

  /**
   * Tổng số đánh giá
   */
  totalRatings: number;
}

/**
 * Paginated result interface
 */
export interface PaginatedResult<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}
