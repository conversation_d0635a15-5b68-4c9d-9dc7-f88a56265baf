import React, { useState, useEffect } from 'react';
import {
  <PERSON>ton,
  <PERSON>,
  Typography,
  Input,
  Textarea,
  FormItem,
  Form,
} from '../../../shared/components/common';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { z } from 'zod';
import ChatLayout from '../components/ChatLayout';
import { ImportedConversation, CreateFineTuneDatasetDto } from '../types/dataset.types';
import { useCreateFineTuneDataset, useUploadJsonlData } from '../hooks/useDatasetQuery';
import { convertConversationsToJsonl } from '../services/dataset.service';
import { FileText, BarChart3, Database, Upload } from 'lucide-react';

// Schema validation cho form
const DatasetFormSchema = z.object({
  name: z.string().min(1, 'Dataset name is required'),
  description: z.string().min(1, 'Dataset description is required'),
  provider: z.string().default('OPENAI'),
});

/**
 * <PERSON>rang quản lý dataset tổng hợp (Training + Validation)
 */
type DatasetFormData = z.infer<typeof DatasetFormSchema>;

// Keys cho localStorage
const STORAGE_KEYS = {
  TRAINING_CONVERSATIONS: 'dataset_training_conversations',
  VALIDATION_CONVERSATIONS: 'dataset_validation_conversations',
  ACTIVE_TAB: 'dataset_active_tab',
};

// Helper functions cho localStorage
const saveToStorage = (key: string, data: ImportedConversation[] | string) => {
  try {
    localStorage.setItem(key, JSON.stringify(data));
  } catch (error) {
    console.warn('Failed to save to localStorage:', error);
  }
};

const loadFromStorage = (
  key: string,
  defaultValue: ImportedConversation[] | string
): ImportedConversation[] | string => {
  try {
    const stored = localStorage.getItem(key);
    if (stored) {
      const parsed = JSON.parse(stored);
      // Chuyển đổi createdAt từ string về Date nếu cần cho conversations
      if (Array.isArray(parsed)) {
        return parsed.map((item: ImportedConversation) => ({
          ...item,
          createdAt: item.createdAt ? new Date(item.createdAt) : new Date(),
        }));
      }
      return parsed;
    }
  } catch (error) {
    console.warn('Failed to load from localStorage:', error);
  }
  return defaultValue;
};

const clearStorage = () => {
  try {
    Object.values(STORAGE_KEYS).forEach(key => {
      localStorage.removeItem(key);
    });
  } catch (error) {
    console.warn('Failed to clear localStorage:', error);
  }
};

const DatasetManagementPage: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  // State để chia sẻ dữ liệu giữa training và validation với localStorage
  const [trainingConversations, setTrainingConversations] = useState<ImportedConversation[]>(
    () => loadFromStorage(STORAGE_KEYS.TRAINING_CONVERSATIONS, []) as ImportedConversation[]
  );
  const [validationConversations, setValidationConversations] = useState<ImportedConversation[]>(
    () =>
      loadFromStorage(
        STORAGE_KEYS.VALIDATION_CONVERSATIONS,
        [] as ImportedConversation[]
      ) as ImportedConversation[]
  );
  const [activeTab, setActiveTab] = useState<string>(
    () => loadFromStorage(STORAGE_KEYS.ACTIVE_TAB, 'training') as string
  );
  const [showCreateForm, setShowCreateForm] = useState(false);

  // API hooks
  const createFineTuneDataset = useCreateFineTuneDataset();
  const uploadJsonlData = useUploadJsonlData();

  // Lưu dữ liệu vào localStorage khi state thay đổi
  useEffect(() => {
    saveToStorage(STORAGE_KEYS.TRAINING_CONVERSATIONS, trainingConversations);
  }, [trainingConversations]);

  useEffect(() => {
    saveToStorage(STORAGE_KEYS.VALIDATION_CONVERSATIONS, validationConversations);
  }, [validationConversations]);

  useEffect(() => {
    saveToStorage(STORAGE_KEYS.ACTIVE_TAB, activeTab);
  }, [activeTab]);

  // Handle training data changes
  const handleTrainingDataChange = (conversations: ImportedConversation[]) => {
    setTrainingConversations(conversations);
  };

  // Handle validation data changes
  const handleValidationDataChange = (conversations: ImportedConversation[]) => {
    setValidationConversations(conversations);
  };

  // Auto split training data to validation
  const handleAutoSplit = () => {
    if (trainingConversations.length > 0) {
      const splitIndex = Math.floor(trainingConversations.length * 0.8);
      const validationData = trainingConversations.slice(splitIndex).map(conv => ({
        ...conv,
        id: `val_${conv.id}`,
        title: `[Validation] ${conv.title}`,
      }));

      setValidationConversations(prev => [...prev, ...validationData]);
      setActiveTab('validation');
    }
  };

  // Handle form submit
  const handleFormSubmit = async (data: Record<string, unknown>) => {
    const formData = data as DatasetFormData;
    try {
      // Validate that we have data to submit
      if (trainingConversations.length === 0 && validationConversations.length === 0) {
        console.error('No training or validation data available');
        return;
      }

      let trainDatasetPath = '';
      let validDatasetPath = '';

      // Upload training data if available
      if (trainingConversations.length > 0) {
        const trainJsonl = convertConversationsToJsonl(trainingConversations);
        const trainFilename = `${formData.name}_train_${Date.now()}.jsonl`;
        trainDatasetPath = await uploadJsonlData.mutateAsync({
          jsonlData: trainJsonl,
          filename: trainFilename,
        });
      }

      // Upload validation data if available
      if (validationConversations.length > 0) {
        const validJsonl = convertConversationsToJsonl(validationConversations);
        const validFilename = `${formData.name}_valid_${Date.now()}.jsonl`;
        validDatasetPath = await uploadJsonlData.mutateAsync({
          jsonlData: validJsonl,
          filename: validFilename,
        });
      }

      // Create fine-tune dataset with actual file paths
      const fineTuneData: CreateFineTuneDatasetDto = {
        name: formData.name,
        description: formData.description,
        provider: formData.provider,
        trainDataset: trainDatasetPath || 'application/jsonl',
        validDataset: validDatasetPath || 'application/jsonl',
      };

      await createFineTuneDataset.mutateAsync(fineTuneData);

      // Reset form and navigate
      setTrainingConversations([]);
      setValidationConversations([]);
      setShowCreateForm(false);
      // Clear localStorage
      clearStorage();
      navigate('/model-training/datasets');
    } catch (error) {
      console.error('Error creating dataset:', error);
      // You might want to show a toast notification here
    }
  };

  return (
    <div className="space-y-6">
      {/* Navigation */}
      <div className="flex items-center justify-between">
        {/* Button Group Navigation */}
        <div className="flex space-x-1 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg">
          <Button
            variant={activeTab === 'training' ? 'primary' : 'ghost'}
            size="sm"
            onClick={() => setActiveTab('training')}
            className="flex items-center"
          >
            <FileText size={16} className="mr-2" />
            {t('Training Data')}
          </Button>
          <Button
            variant={activeTab === 'validation' ? 'primary' : 'ghost'}
            size="sm"
            onClick={() => setActiveTab('validation')}
            className="flex items-center"
          >
            <BarChart3 size={16} className="mr-2" />
            {t('Validation Data')}
          </Button>
        </div>

        {/* Quick Actions */}
        <div className="flex space-x-2">
          {trainingConversations.length > 0 && (
            <Button onClick={handleAutoSplit} variant="outline" size="sm">
              {t('Auto Split 80/20')}
            </Button>
          )}

          {/* Clear Data Button */}
          {(trainingConversations.length > 0 || validationConversations.length > 0) && (
            <Button
              onClick={() => {
                setTrainingConversations([]);
                setValidationConversations([]);
                clearStorage();
              }}
              variant="outline"
              size="sm"
              className="text-red-600 hover:text-red-700"
            >
              {t('Clear All Data')}
            </Button>
          )}

          {/* Create Dataset Button */}
          {(trainingConversations.length > 0 || validationConversations.length > 0) && (
            <Button
              onClick={() => setShowCreateForm(true)}
              variant="primary"
              size="sm"
              className="flex items-center"
            >
              <Database size={16} className="mr-2" />
              {t('Create Fine-tune Dataset')}
            </Button>
          )}
        </div>
      </div>

      {/* Create Dataset Form */}
      {showCreateForm && (
        <Card>
          <div className="p-6 space-y-4">
            <Typography variant="h6" className="flex items-center mb-4">
              <Database className="mr-2 text-primary" size={20} />
              {t('Create Fine-tune Dataset')}
            </Typography>

            <Form
              schema={DatasetFormSchema}
              onSubmit={handleFormSubmit}
              defaultValues={{
                name: '',
                description: '',
                provider: 'OPENAI',
              }}
              className="space-y-4"
            >
              <div className="grid grid-cols-1 gap-4">
                <FormItem name="name" label={t('Dataset Name')} required>
                  <Input placeholder={t('Enter dataset name')} fullWidth />
                </FormItem>

                <FormItem name="description" label={t('Description')} required>
                  <Textarea placeholder={t('Enter dataset description')} rows={3} fullWidth />
                </FormItem>

                <FormItem name="provider" label={t('Provider')}>
                  <Input placeholder="OPENAI" disabled fullWidth />
                </FormItem>
              </div>

              <div className="flex justify-between items-center pt-4">
                <div className="text-sm text-gray-600">
                  <span>Training: {trainingConversations.length} conversations</span>
                  <span className="mx-2">•</span>
                  <span>Validation: {validationConversations.length} conversations</span>
                </div>

                <div className="flex space-x-2">
                  <Button type="button" variant="outline" onClick={() => setShowCreateForm(false)}>
                    {t('Cancel')}
                  </Button>
                  <Button
                    type="submit"
                    isLoading={createFineTuneDataset.isPending || uploadJsonlData.isPending}
                    disabled={
                      trainingConversations.length === 0 && validationConversations.length === 0
                    }
                    className="flex items-center"
                  >
                    <Upload size={16} className="mr-2" />
                    {t('Create Dataset')}
                  </Button>
                </div>
              </div>
            </Form>
          </div>
        </Card>
      )}

      {/* Content */}
      <div className="mt-6">
        {/* Training Data Content */}
        {activeTab === 'training' && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-500">
                {trainingConversations.length > 0 && (
                  <span>
                    {trainingConversations.length} conversations • Recommended: 80% of total data
                  </span>
                )}
              </div>
            </div>

            <Card>
              <div className="h-[600px]">
                <ChatLayout onConversationsChange={handleTrainingDataChange} />
              </div>
            </Card>
          </div>
        )}

        {/* Validation Data Content */}
        {activeTab === 'validation' && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-500">
                {validationConversations.length > 0 && (
                  <span>
                    {validationConversations.length} conversations • Recommended: 20% of total data
                  </span>
                )}
              </div>
            </div>

            <Card>
              <div className="h-[600px]">
                <ChatLayout onConversationsChange={handleValidationDataChange} />
              </div>
            </Card>
          </div>
        )}
      </div>
    </div>
  );
};

export default DatasetManagementPage;
