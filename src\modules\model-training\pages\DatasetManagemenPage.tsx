import React, { useState } from 'react';
import {
  <PERSON>ton,
  Card,
  Typography,
  Input,
  Textarea,
  FormItem,
  Form,
} from '../../../shared/components/common';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { z } from 'zod';
import ChatLayout from '../components/ChatLayout';
import { ImportedConversation, CreateFineTuneDatasetDto } from '../types/dataset.types';
import { useCreateFineTuneDataset, useUploadJsonlData } from '../hooks/useDatasetQuery';
import { convertConversationsToJsonl } from '../services/dataset.service';
import { FileText, BarChart3, Database, Upload } from 'lucide-react';

// Schema validation cho form
const DatasetFormSchema = z.object({
  name: z.string().min(1, 'Dataset name is required'),
  description: z.string().min(1, 'Dataset description is required'),
  provider: z.string().default('OPENAI'),
});

/**
 * Trang quản lý dataset tổng hợp (Training + Validation)
 */
type DatasetFormData = z.infer<typeof DatasetFormSchema>;

const DatasetManagementPage: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  // State để chia sẻ dữ liệu giữa training và validation
  const [trainingConversations, setTrainingConversations] = useState<ImportedConversation[]>([]);
  const [validationConversations, setValidationConversations] = useState<ImportedConversation[]>(
    []
  );
  const [activeTab, setActiveTab] = useState('training');
  const [showCreateForm, setShowCreateForm] = useState(false);

  // API hooks
  const createFineTuneDataset = useCreateFineTuneDataset();
  const uploadJsonlData = useUploadJsonlData();

  // Handle training data changes
  const handleTrainingDataChange = (conversations: ImportedConversation[]) => {
    setTrainingConversations(conversations);
  };

  // Handle validation data changes
  const handleValidationDataChange = (conversations: ImportedConversation[]) => {
    setValidationConversations(conversations);
  };

  // Auto split training data to validation
  const handleAutoSplit = () => {
    if (trainingConversations.length > 0) {
      const splitIndex = Math.floor(trainingConversations.length * 0.8);
      const validationData = trainingConversations.slice(splitIndex).map(conv => ({
        ...conv,
        id: `val_${conv.id}`,
        title: `[Validation] ${conv.title}`,
      }));

      setValidationConversations(prev => [...prev, ...validationData]);
      setActiveTab('validation');
    }
  };

  // Handle form submit
  const handleFormSubmit = async (data: DatasetFormData) => {
    try {
      // Validate that we have data to submit
      if (trainingConversations.length === 0 && validationConversations.length === 0) {
        console.error('No training or validation data available');
        return;
      }

      let trainDatasetPath = '';
      let validDatasetPath = '';

      // Upload training data if available
      if (trainingConversations.length > 0) {
        const trainJsonl = convertConversationsToJsonl(trainingConversations);
        const trainFilename = `${data.name}_train_${Date.now()}.jsonl`;
        trainDatasetPath = await uploadJsonlData.mutateAsync({
          jsonlData: trainJsonl,
          filename: trainFilename,
        });
      }

      // Upload validation data if available
      if (validationConversations.length > 0) {
        const validJsonl = convertConversationsToJsonl(validationConversations);
        const validFilename = `${data.name}_valid_${Date.now()}.jsonl`;
        validDatasetPath = await uploadJsonlData.mutateAsync({
          jsonlData: validJsonl,
          filename: validFilename,
        });
      }

      // Create fine-tune dataset with actual file paths
      const fineTuneData: CreateFineTuneDatasetDto = {
        name: data.name,
        description: data.description,
        provider: data.provider,
        trainDataset: trainDatasetPath || 'application/jsonl',
        validDataset: validDatasetPath || 'application/jsonl',
      };

      await createFineTuneDataset.mutateAsync(fineTuneData);

      // Reset form and navigate
      setTrainingConversations([]);
      setValidationConversations([]);
      setShowCreateForm(false);
      navigate('/model-training/datasets');
    } catch (error) {
      console.error('Error creating dataset:', error);
      // You might want to show a toast notification here
    }
  };

  return (
    <div className="space-y-6">
      {/* Navigation */}
      <div className="flex items-center justify-between">
        {/* Button Group Navigation */}
        <div className="flex space-x-1 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg">
          <Button
            variant={activeTab === 'training' ? 'primary' : 'ghost'}
            size="sm"
            onClick={() => setActiveTab('training')}
            className="flex items-center"
          >
            <FileText size={16} className="mr-2" />
            {t('Training Data')}
          </Button>
          <Button
            variant={activeTab === 'validation' ? 'primary' : 'ghost'}
            size="sm"
            onClick={() => setActiveTab('validation')}
            className="flex items-center"
          >
            <BarChart3 size={16} className="mr-2" />
            {t('Validation Data')}
          </Button>
        </div>

        {/* Quick Actions */}
        <div className="flex space-x-2">
          {trainingConversations.length > 0 && (
            <Button onClick={handleAutoSplit} variant="outline" size="sm">
              {t('Auto Split 80/20')}
            </Button>
          )}

          {/* Create Dataset Button */}
          {(trainingConversations.length > 0 || validationConversations.length > 0) && (
            <Button
              onClick={() => setShowCreateForm(true)}
              variant="primary"
              size="sm"
              className="flex items-center"
            >
              <Database size={16} className="mr-2" />
              {t('Create Fine-tune Dataset')}
            </Button>
          )}
        </div>
      </div>

      {/* Create Dataset Form */}
      {showCreateForm && (
        <Card>
          <div className="p-6 space-y-4">
            <Typography variant="h6" className="flex items-center mb-4">
              <Database className="mr-2 text-primary" size={20} />
              {t('Create Fine-tune Dataset')}
            </Typography>

            <Form
              schema={DatasetFormSchema}
              onSubmit={() => handleFormSubmit}
              defaultValues={{
                name: '',
                description: '',
                provider: 'OPENAI',
              }}
              className="space-y-4"
            >
              <div className="grid grid-cols-1 gap-4">
                <FormItem name="name" label={t('Dataset Name')} required>
                  <Input placeholder={t('Enter dataset name')} fullWidth />
                </FormItem>

                <FormItem name="description" label={t('Description')} required>
                  <Textarea placeholder={t('Enter dataset description')} rows={3} fullWidth />
                </FormItem>

                <FormItem name="provider" label={t('Provider')}>
                  <Input placeholder="OPENAI" disabled fullWidth />
                </FormItem>
              </div>

              <div className="flex justify-between items-center pt-4">
                <div className="text-sm text-gray-600">
                  <span>Training: {trainingConversations.length} conversations</span>
                  <span className="mx-2">•</span>
                  <span>Validation: {validationConversations.length} conversations</span>
                </div>

                <div className="flex space-x-2">
                  <Button type="button" variant="outline" onClick={() => setShowCreateForm(false)}>
                    {t('Cancel')}
                  </Button>
                  <Button
                    type="submit"
                    isLoading={createFineTuneDataset.isPending || uploadJsonlData.isPending}
                    disabled={
                      trainingConversations.length === 0 && validationConversations.length === 0
                    }
                    className="flex items-center"
                  >
                    <Upload size={16} className="mr-2" />
                    {t('Create Dataset')}
                  </Button>
                </div>
              </div>
            </Form>
          </div>
        </Card>
      )}

      {/* Content */}
      <div className="mt-6">
        {/* Training Data Content */}
        {activeTab === 'training' && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-500">
                {trainingConversations.length > 0 && (
                  <span>
                    {trainingConversations.length} conversations • Recommended: 80% of total data
                  </span>
                )}
              </div>
            </div>

            <Card>
              <div className="h-[600px]">
                <ChatLayout onConversationsChange={handleTrainingDataChange} />
              </div>
            </Card>
          </div>
        )}

        {/* Validation Data Content */}
        {activeTab === 'validation' && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-500">
                {validationConversations.length > 0 && (
                  <span>
                    {validationConversations.length} conversations • Recommended: 20% of total data
                  </span>
                )}
              </div>
            </div>

            <Card>
              <div className="h-[600px]">
                <ChatLayout onConversationsChange={handleValidationDataChange} />
              </div>
            </Card>
          </div>
        )}
      </div>
    </div>
  );
};

export default DatasetManagementPage;
