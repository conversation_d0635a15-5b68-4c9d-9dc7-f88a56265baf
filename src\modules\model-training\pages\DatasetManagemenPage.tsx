import React, { useState } from 'react';
import {
  <PERSON>ton,
  Card,
  Typography,
  Input,
  Textarea,
  FormItem,
} from '../../../shared/components/common';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import ChatLayout from '../components/ChatLayout';
import { ImportedConversation, CreateFineTuneDatasetDto } from '../types/dataset.types';
import { useCreateFineTuneDataset } from '../hooks/useDatasetQuery';
import { FileText, BarChart3, Database, Upload } from 'lucide-react';

// Schema validation cho form
const DatasetFormSchema = z.object({
  name: z.string().min(1, 'Dataset name is required'),
  description: z.string().min(1, 'Dataset description is required'),
  provider: z.string().default('OPENAI'),
});

/**
 * Trang quản lý dataset tổng hợp (Training + Validation)
 */
type DatasetFormData = z.infer<typeof DatasetFormSchema>;

const DatasetManagementPage: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  // State để chia sẻ dữ liệu giữa training và validation
  const [trainingConversations, setTrainingConversations] = useState<ImportedConversation[]>([]);
  const [validationConversations, setValidationConversations] = useState<ImportedConversation[]>(
    []
  );
  const [activeTab, setActiveTab] = useState('training');
  const [showCreateForm, setShowCreateForm] = useState(false);

  // API hooks
  const createFineTuneDataset = useCreateFineTuneDataset();

  // Form setup
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<DatasetFormData>({
    resolver: zodResolver(DatasetFormSchema),
    defaultValues: {
      name: '',
      description: '',
      provider: 'OPENAI',
    },
  });

  // Handle training data changes
  const handleTrainingDataChange = (conversations: ImportedConversation[]) => {
    setTrainingConversations(conversations);
  };

  // Handle validation data changes
  const handleValidationDataChange = (conversations: ImportedConversation[]) => {
    setValidationConversations(conversations);
  };

  // Auto split training data to validation
  const handleAutoSplit = () => {
    if (trainingConversations.length > 0) {
      const splitIndex = Math.floor(trainingConversations.length * 0.8);
      const validationData = trainingConversations.slice(splitIndex).map(conv => ({
        ...conv,
        id: `val_${conv.id}`,
        title: `[Validation] ${conv.title}`,
      }));

      setValidationConversations(prev => [...prev, ...validationData]);
      setActiveTab('validation');
    }
  };

  // Handle form submit
  const onSubmit = async (data: DatasetFormData) => {
    try {
      // Create fine-tune dataset với format API mới
      const fineTuneData: CreateFineTuneDatasetDto = {
        name: data.name,
        description: data.description,
        provider: data.provider,
        trainDataset: 'application/jsonl',
        validDataset: 'application/jsonl',
      };

      await createFineTuneDataset.mutateAsync(fineTuneData);

      // Reset form and navigate
      reset();
      setTrainingConversations([]);
      setValidationConversations([]);
      setShowCreateForm(false);
      navigate('/model-training/datasets');
    } catch (error) {
      console.error('Error creating dataset:', error);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <Typography variant="h4" className="flex items-center">
            <Database className="mr-3 text-primary" size={28} />
            {t('Dataset Management')}
          </Typography>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            {t('Manage training and validation data for fine-tuning models')}
          </p>
        </div>
      </div>

      {/* Navigation */}
      <div className="flex items-center justify-between">
        {/* Button Group Navigation */}
        <div className="flex space-x-1 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg">
          <Button
            variant={activeTab === 'training' ? 'primary' : 'ghost'}
            size="sm"
            onClick={() => setActiveTab('training')}
            className="flex items-center"
          >
            <FileText size={16} className="mr-2" />
            {t('Training Data')}
          </Button>
          <Button
            variant={activeTab === 'validation' ? 'primary' : 'ghost'}
            size="sm"
            onClick={() => setActiveTab('validation')}
            className="flex items-center"
          >
            <BarChart3 size={16} className="mr-2" />
            {t('Validation Data')}
          </Button>
        </div>

        {/* Quick Actions */}
        <div className="flex space-x-2">
          {trainingConversations.length > 0 && (
            <Button onClick={handleAutoSplit} variant="outline" size="sm">
              {t('Auto Split 80/20')}
            </Button>
          )}

          {/* Create Dataset Button */}
          {(trainingConversations.length > 0 || validationConversations.length > 0) && (
            <Button
              onClick={() => setShowCreateForm(true)}
              variant="primary"
              size="sm"
              className="flex items-center"
            >
              <Database size={16} className="mr-2" />
              {t('Create Fine-tune Dataset')}
            </Button>
          )}
        </div>
      </div>

      {/* Statistics */}
      <Card className="p-4">
        <div className="flex items-center space-x-6">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{trainingConversations.length}</div>
            <div className="text-sm text-gray-500">Training</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {validationConversations.length}
            </div>
            <div className="text-sm text-gray-500">Validation</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">
              {trainingConversations.length + validationConversations.length}
            </div>
            <div className="text-sm text-gray-500">Total</div>
          </div>
        </div>
      </Card>

      {/* Create Dataset Form */}
      {showCreateForm && (
        <Card>
          <form onSubmit={handleSubmit(onSubmit)} className="p-6 space-y-4">
            <Typography variant="h6" className="flex items-center mb-4">
              <Database className="mr-2 text-primary" size={20} />
              {t('Create Fine-tune Dataset')}
            </Typography>

            <div className="grid grid-cols-1 gap-4">
              <FormItem
                name="name"
                label={t('Dataset Name')}
                helpText={errors.name?.message}
                required
              >
                <Input
                  {...register('name')}
                  placeholder={t('Enter dataset name')}
                  error={errors.name?.message as string}
                  fullWidth
                />
              </FormItem>

              <FormItem
                name="description"
                label={t('Description')}
                helpText={errors.description?.message}
                required
              >
                <Textarea
                  {...register('description')}
                  placeholder={t('Enter dataset description')}
                  status={errors.description?.message ? 'error' : 'default'}
                  rows={3}
                  fullWidth
                />
              </FormItem>

              <FormItem name="provider" label={t('Provider')} helpText={errors.provider?.message}>
                <Input {...register('provider')} placeholder="OPENAI" disabled fullWidth />
              </FormItem>
            </div>

            <div className="flex justify-between items-center pt-4">
              <div className="text-sm text-gray-600">
                <span>Training: {trainingConversations.length} conversations</span>
                <span className="mx-2">•</span>
                <span>Validation: {validationConversations.length} conversations</span>
              </div>

              <div className="flex space-x-2">
                <Button type="button" variant="outline" onClick={() => setShowCreateForm(false)}>
                  {t('Cancel')}
                </Button>
                <Button
                  type="submit"
                  isLoading={createFineTuneDataset.isPending}
                  disabled={
                    trainingConversations.length === 0 && validationConversations.length === 0
                  }
                  className="flex items-center"
                >
                  <Upload size={16} className="mr-2" />
                  {t('Create Dataset')}
                </Button>
              </div>
            </div>
          </form>
        </Card>
      )}

      {/* Content */}
      <div className="mt-6">
        {/* Training Data Content */}
        {activeTab === 'training' && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Typography variant="h6" className="flex items-center">
                <FileText className="mr-2 text-blue-600" size={20} />
                {t('Training Dataset')}
              </Typography>
              <div className="text-sm text-gray-500">
                {trainingConversations.length > 0 && (
                  <span>
                    {trainingConversations.length} conversations • Recommended: 80% of total data
                  </span>
                )}
              </div>
            </div>

            <Card>
              <div className="h-[600px]">
                <ChatLayout onConversationsChange={handleTrainingDataChange} />
              </div>
            </Card>
          </div>
        )}

        {/* Validation Data Content */}
        {activeTab === 'validation' && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Typography variant="h6" className="flex items-center">
                <BarChart3 className="mr-2 text-green-600" size={20} />
                {t('Validation Dataset')}
              </Typography>
              <div className="text-sm text-gray-500">
                {validationConversations.length > 0 && (
                  <span>
                    {validationConversations.length} conversations • Recommended: 20% of total data
                  </span>
                )}
              </div>
            </div>

            <Card>
              <div className="h-[600px]">
                <ChatLayout onConversationsChange={handleValidationDataChange} />
              </div>
            </Card>
          </div>
        )}
      </div>

      {/* Tips */}
      <Card className="p-4 bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800">
        <Typography variant="h6" className="text-blue-800 dark:text-blue-200 mb-2">
          💡 {t('Tips')}
        </Typography>
        <ul className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
          <li>• {t('Training data is used to train the model')}</li>
          <li>• {t('Validation data is used to evaluate model performance')}</li>
          <li>• {t('Recommended ratio: 80% training, 20% validation')}</li>
          <li>• {t('API format: name, description, provider, trainDataset, validDataset')}</li>
        </ul>
      </Card>
    </div>
  );
};

export default DatasetManagementPage;
