import React from 'react';
import { useTranslation } from 'react-i18next';
import { Typography } from '@/shared/components/common';
import { Brain } from 'lucide-react';

/**
 * Model Fine-tune Page - Quản lý model fine-tune
 */
const ModelFineTunePage: React.FC = () => {
  const { t } = useTranslation(['user-dataset']);

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="text-center py-16">
        <Brain className="mx-auto text-primary mb-4" size={64} />
        <Typography variant="h4" className="mb-4">
          {t('user-dataset:modelFineTune.title', 'Model Fine-tune')}
        </Typography>
        <Typography variant="body1" className="text-gray-600 mb-8">
          {t('user-dataset:modelFineTune.description', 'Tạo và quản lý các model đã được fine-tune từ dataset của bạn.')}
        </Typography>
        <Typography variant="body2" className="text-gray-500">
          {t('Tính năng đang được phát triển...')}
        </Typography>
      </div>
    </div>
  );
};

export default ModelFineTunePage;
