import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, Card } from '@/shared/components/common';
import { useTranslation } from 'react-i18next';

interface ProviderSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSelectOpenAI: () => void;
  onSelectGoogle: () => void;
}

/**
 * Modal để lựa chọn provider khi tạo dataset fine-tune
 */
const ProviderSelectionModal: React.FC<ProviderSelectionModalProps> = ({
  isOpen,
  onClose,
  onSelectOpenAI,
  onSelectGoogle,
}) => {
  const { t } = useTranslation();

  return (
    <Modal isOpen={isOpen} onClose={onClose} title={t('Chọn Provider')} size="md">
      <div className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* OpenAI Option */}
          <Card
            className="p-6 hover:shadow-lg transition-shadow duration-200 cursor-pointer border-2 hover:border-green-500"
            onClick={onSelectOpenAI}
          >
            <div className="text-center">
              <Button
                variant="primary"
                size="sm"
                className="w-full bg-green-600 hover:bg-green-700"
                onClick={e => {
                  e.stopPropagation();
                  onSelectOpenAI();
                }}
              >
                {t('Chọn OpenAI')}
              </Button>
            </div>
          </Card>

          {/* Google Option */}
          <Card
            className="p-6 hover:shadow-lg transition-shadow duration-200 cursor-pointer border-2 hover:border-blue-500"
            onClick={onSelectGoogle}
          >
            <div className="text-center">
              <Button
                variant="primary"
                size="sm"
                className="w-full bg-blue-600 hover:bg-blue-700"
                onClick={e => {
                  e.stopPropagation();
                  onSelectGoogle();
                }}
              >
                {t('Chọn Google')}
              </Button>
            </div>
          </Card>
        </div>

        <div className="flex justify-center mt-6">
          <Button variant="outline" onClick={onClose}>
            {t('Hủy')}
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default ProviderSelectionModal;
