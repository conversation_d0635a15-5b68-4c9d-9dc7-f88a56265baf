import React from 'react';
import { Outlet, useLocation, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  Typography,
  Button,
  Tabs,
} from '@/shared/components/common';
import {
  Database,
  Brain,
  Key,
  Settings,
  Plus,
} from 'lucide-react';

/**
 * User Dataset Layout - Layout chung cho module user-dataset
 */
const UserDatasetLayout: React.FC = () => {
  const { t } = useTranslation();
  const location = useLocation();
  const navigate = useNavigate();

  // Determine active tab based on current path
  const getActiveTab = () => {
    const path = location.pathname;
    if (path.includes('/model-fine-tune')) return 'model-fine-tune';
    if (path.includes('/mode-base')) return 'mode-base';
    if (path.includes('/key-llm')) return 'key-llm';
    return 'data-fine-tune';
  };

  const handleTabChange = (key: string) => {
    switch (key) {
      case 'data-fine-tune':
        navigate('/user-dataset');
        break;
      case 'model-fine-tune':
        navigate('/user-dataset/model-fine-tune');
        break;
      case 'mode-base':
        navigate('/user-dataset/mode-base');
        break;
      case 'key-llm':
        navigate('/user-dataset/key-llm');
        break;
    }
  };

  const tabs = [
    {
      key: 'data-fine-tune',
      label: (
        <span className="flex items-center">
          <Database size={16} className="mr-2" />
          {t('Dataset')}
        </span>
      ),
    },
    {
      key: 'model-fine-tune',
      label: (
        <span className="flex items-center">
          <Brain size={16} className="mr-2" />
          {t('Model Fine-tune')}
        </span>
      ),
    },
    {
      key: 'mode-base',
      label: (
        <span className="flex items-center">
          <Settings size={16} className="mr-2" />
          {t('Base Models')}
        </span>
      ),
    },
    {
      key: 'key-llm',
      label: (
        <span className="flex items-center">
          <Key size={16} className="mr-2" />
          {t('API Keys')}
        </span>
      ),
    },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between py-6">
            <div>
              <Typography variant="h2" className="flex items-center">
                <Database className="mr-3 text-primary" size={32} />
                {t('Quản lý Dataset & Model')}
              </Typography>
              <Typography variant="body1" className="text-gray-600 mt-2">
                {t('Tạo và quản lý dataset, fine-tune model AI của bạn')}
              </Typography>
            </div>

            <Button
              onClick={() => navigate('/user-dataset/create')}
              className="flex items-center"
            >
              <Plus size={16} className="mr-2" />
              {t('Tạo mới')}
            </Button>
          </div>

          {/* Navigation Tabs */}
          <Tabs
            activeKey={getActiveTab()}
            onChange={handleTabChange}
            items={tabs}
            className="border-none"
          />
        </div>
      </div>

      {/* Content */}
      <div className="container mx-auto px-4 py-8">
        <Outlet />
      </div>
    </div>
  );
};

export default UserDatasetLayout;
