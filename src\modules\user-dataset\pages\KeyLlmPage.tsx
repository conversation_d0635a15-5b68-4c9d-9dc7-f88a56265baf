import React from 'react';
import { useTranslation } from 'react-i18next';
import { Typography } from '@/shared/components/common';
import { Key } from 'lucide-react';

/**
 * Key LLM Page - Quản lý API keys
 */
const KeyLlmPage: React.FC = () => {
  const { t } = useTranslation(['user-dataset']);

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="text-center py-16">
        <Key className="mx-auto text-primary mb-4" size={64} />
        <Typography variant="h4" className="mb-4">
          {t('user-dataset:keyLlm.title', 'API Keys')}
        </Typography>
        <Typography variant="body1" className="text-gray-600 mb-8">
          {t('user-dataset:keyLlm.description', 'Quản lý các API key để kết nối với các nhà cung cấp AI.')}
        </Typography>
        <Typography variant="body2" className="text-gray-500">
          {t('Tính năng đang được phát triển...')}
        </Typography>
      </div>
    </div>
  );
};

export default KeyLlmPage;
