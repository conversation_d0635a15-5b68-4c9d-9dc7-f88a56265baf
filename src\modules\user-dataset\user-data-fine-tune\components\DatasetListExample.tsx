import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import { useTranslation } from 'react-i18next';
import DatasetList from './DatasetList';
import { useDeleteUserDataFineTune } from '../hooks/useUserDataFineTune';

/**
 * Example component showing how to use DatasetList
 */
const DatasetListExample: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const deleteDataset = useDeleteUserDataFineTune();

  const handleCreateNew = () => {
    // Navigate to create dataset page
    navigate('/user-dataset/create');
  };

  const handleSelectDataset = (id: string) => {
    // Navigate to dataset detail page
    navigate(`/user-dataset/${id}`);
  };

  const handleEditDataset = (id: string) => {
    // Navigate to edit dataset page
    navigate(`/user-dataset/${id}/edit`);
  };

  const handleDeleteDataset = async (id: string, name: string) => {
    if (window.confirm(t('Bạn có chắc chắn muốn xóa dataset "{name}"?', { name }))) {
      try {
        await deleteDataset.mutateAsync(id);
        toast.success(t('Đã xóa dataset thành công'));
      } catch (error) {
        toast.error(t('Có lỗi xảy ra khi xóa dataset'));
        console.error('Delete dataset error:', error);
      }
    }
  };

  const handleDownloadDataset = (id: string) => {
    // Implement download logic
    toast.info(t('Tính năng tải xuống đang được phát triển'));
  };

  const handleDuplicateDataset = (id: string) => {
    // Navigate to duplicate dataset page
    navigate(`/user-dataset/${id}/duplicate`);
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <DatasetList
        onCreateNew={handleCreateNew}
        onSelectDataset={handleSelectDataset}
      />
    </div>
  );
};

export default DatasetListExample;
