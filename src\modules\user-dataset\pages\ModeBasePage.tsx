import React from 'react';
import { useTranslation } from 'react-i18next';
import { Typography } from '@/shared/components/common';
import { Settings } from 'lucide-react';

/**
 * Mode Base Page - Quản lý base models
 */
const ModeBasePage: React.FC = () => {
  const { t } = useTranslation(['user-dataset']);

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="text-center py-16">
        <Settings className="mx-auto text-primary mb-4" size={64} />
        <Typography variant="h4" className="mb-4">
          {t('user-dataset:modeBase.title', 'Base Models')}
        </Typography>
        <Typography variant="body1" className="text-gray-600 mb-8">
          {t('user-dataset:modeBase.description', 'Khám phá và so sánh các base model có sẵn để fine-tune.')}
        </Typography>
        <Typography variant="body2" className="text-gray-500">
          {t('Tính năng đang được phát triển...')}
        </Typography>
      </div>
    </div>
  );
};

export default ModeBasePage;
