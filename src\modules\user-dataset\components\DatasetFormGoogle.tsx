import React, { useState } from 'react';
import { Card, Input, Button, FormItem, Textarea } from '@/shared/components/common';
import { useTranslation } from 'react-i18next';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  CreateUserDataFineTuneDto,
  ProviderFineTuneEnum,
} from '../user-data-fine-tune/types/user-data-fine-tune.types';
import { useCreateAndUploadDataset } from '../user-data-fine-tune/hooks/useUserDataFineTune';
import {
  convertConversationsToJsonl,
  validateJsonlData,
} from '../user-data-fine-tune/services/user-data-fine-tune.service';
import ChatLayout from './ChatLayout';
import ChatPanel from './ChatPanel';

// Schema validation cho Google dataset form
const GoogleDatasetFormSchema = z.object({
  name: z.string().min(1, 'Dataset name is required'),
  description: z.string().min(1, 'Dataset description is required'),
});

type GoogleDatasetFormData = z.infer<typeof GoogleDatasetFormSchema>;

// Temporary type for ImportedConversation
interface ImportedConversation {
  id: string;
  title: string;
  messages: Array<{
    role: 'system' | 'user' | 'assistant';
    content: string;
  }>;
  createdAt: Date;
}

interface DatasetFormGoogleProps {
  /**
   * Callback khi tạo dataset thành công
   */
  onSuccess?: () => void;

  /**
   * Callback khi conversations thay đổi
   */
  onConversationsChange?: (conversations: ImportedConversation[]) => void;
}

/**
 * Component form tạo dataset cho Google (không có ConversationSidebar)
 * Giao diện đơn giản: ChatLayout + ChatPanel + DatasetForm
 */
const DatasetFormGoogle: React.FC<DatasetFormGoogleProps> = ({
  onSuccess,
  onConversationsChange,
}) => {
  const { t } = useTranslation();
  const { createAndUpload, isLoading, error } = useCreateAndUploadDataset();

  // State cho conversations và dataset
  const [conversations, setConversations] = useState<ImportedConversation[]>([]);
  const [showForm, setShowForm] = useState(false);

  // Khởi tạo form với React Hook Form và Zod
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<GoogleDatasetFormData>({
    resolver: zodResolver(GoogleDatasetFormSchema),
    defaultValues: {
      name: '',
      description: '',
    },
  });

  // Handle conversations change từ ChatLayout
  const handleConversationsChange = (updatedConversations: ImportedConversation[]) => {
    setConversations(updatedConversations);
    // Notify parent component
    if (onConversationsChange) {
      onConversationsChange(updatedConversations);
    }
  };

  // Xử lý submit form
  const onSubmit = async (data: GoogleDatasetFormData) => {
    try {
      if (conversations.length === 0) {
        console.error('No conversations available');
        return;
      }

      // Convert conversations to JSONL format
      const trainJsonlData = convertConversationsToJsonl(conversations);

      // Validate JSONL data
      const validation = validateJsonlData(trainJsonlData);
      if (!validation.isValid) {
        console.error('Training data validation failed:', validation.errors);
        return;
      }

      // Create dataset info for Google
      const datasetInfo: CreateUserDataFineTuneDto = {
        name: data.name,
        description: data.description,
        provider: ProviderFineTuneEnum.GOOGLE,
        trainDataset: 'application/jsonl',
        validDataset: undefined, // Google không cần validation data
      };

      // Create and upload dataset
      await createAndUpload({
        datasetInfo,
        trainJsonlData,
        validJsonlData: undefined, // Google không cần validation data
      });

      // Reset form và notify success
      reset();
      setConversations([]);
      setShowForm(false);

      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error('Error creating Google dataset:', error);
    }
  };

  return (
    <div>
      {/* Dataset Form */}
      {showForm && (
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <Card>
            <div className="p-4">
              <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormItem
                    name="name"
                    label={t('Tên Dataset')}
                    helpText={errors.name?.message}
                    required
                  >
                    <Input
                      {...register('name')}
                      placeholder={t('Nhập tên dataset cho Google')}
                      error={errors.name?.message as string}
                      fullWidth
                    />
                  </FormItem>

                  <FormItem
                    name="description"
                    label={t('Mô tả')}
                    helpText={errors.description?.message}
                    required
                  >
                    <Textarea
                      {...register('description')}
                      placeholder={t('Nhập mô tả dataset cho Google AI')}
                      status={errors.description?.message ? 'error' : 'default'}
                      rows={2}
                      fullWidth
                    />
                  </FormItem>
                </div>

                <div className="flex justify-between items-center pt-2">
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    <span className="inline-flex items-center">
                      <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                      Google Provider
                    </span>
                    {conversations.length > 0 && (
                      <span className="ml-4">{conversations.length} conversations</span>
                    )}
                  </div>

                  <div className="flex space-x-2">
                    <Button type="button" variant="outline" onClick={() => setShowForm(false)}>
                      {t('Hủy Form')}
                    </Button>
                    <Button
                      type="submit"
                      isLoading={isLoading}
                      disabled={conversations.length === 0}
                      className="bg-blue-600 hover:bg-blue-700"
                    >
                      {t('Tạo Dataset Google')}
                    </Button>
                  </div>
                </div>
              </form>
            </div>
          </Card>
        </div>
      )}

      {/* Main Content: ChatLayout + ChatPanel (NO ConversationSidebar) */}
      <div className="flex-1 flex overflow-hidden">
        {/* Chat Layout - chiếm toàn bộ không gian */}
        <div className="flex-1 flex flex-col">
          <ChatLayout onConversationsChange={handleConversationsChange} />
        </div>

        {/* Chat Panel - sidebar bên phải */}
        <div className="w-80 border-l border-gray-200 dark:border-gray-700">
          <ChatPanel />
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="p-4 bg-red-50 dark:bg-red-900/20 border-t border-red-200 dark:border-red-800">
          <p className="text-sm text-red-600 dark:text-red-400">
            {t('Lỗi tạo dataset')}: {error.message}
          </p>
        </div>
      )}
    </div>
  );
};

export default DatasetFormGoogle;
