import React, { useState } from 'react';
import { Typography, Pagination, Loading, Button } from '@/shared/components/common';
import { useTranslation } from 'react-i18next';
import { Database } from 'lucide-react';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { ModernMenuItem } from '@/shared/components/common/ModernMenu';
import { useUserDataFineTuneList } from '../user-data-fine-tune/hooks/useUserDataFineTune';
import { DatasetCard } from '../components';
import {
  DataFineTuneStatus,
  UserDataFineTuneQueryDto,
  UserDataFineTuneSortBy,
} from '../user-data-fine-tune/types/user-data-fine-tune.types';

/**
 * Data Fine-tune Page - Hiển thị danh sách dataset fine-tune
 */
const DataFineTunePage: React.FC = () => {
  const { t } = useTranslation();
  const [queryParams, setQueryParams] = useState<UserDataFineTuneQueryDto>({
    page: 1,
    limit: 12,
    search: '',
    status: undefined,
    sortBy: UserDataFineTuneSortBy.CREATED_AT,
  });

  const { data, isLoading, error } = useUserDataFineTuneList(queryParams);

  // Status labels
  const getStatusLabel = (status: DataFineTuneStatus) => {
    switch (status) {
      case DataFineTuneStatus.PENDING:
        return t('Đang chờ');
      case DataFineTuneStatus.PROCESSING:
        return t('Đang xử lý');
      case DataFineTuneStatus.COMPLETED:
        return t('Hoàn thành');
      case DataFineTuneStatus.FAILED:
        return t('Thất bại');
      case DataFineTuneStatus.CANCELLED:
        return t('Đã hủy');
      default:
        return status;
    }
  };

  // Handle search
  const handleSearch = (value: string) => {
    setQueryParams(prev => ({
      ...prev,
      search: value,
      page: 1,
    }));
  };

  // Handle pagination
  const handlePageChange = (page: number) => {
    setQueryParams(prev => ({
      ...prev,
      page,
    }));
  };

  const handleCreateNew = () => {
    // Navigate to create dataset page (sẽ tạo sau)
    console.log('Navigate to create dataset');
  };

  const handleSelectDataset = (id: string) => {
    // Navigate to dataset detail page (sẽ tạo sau)
    console.log('Navigate to dataset detail:', id);
  };

  // Menu items for filter
  const filterMenuItems: ModernMenuItem[] = [
    {
      id: 'all',
      label: t('Tất cả trạng thái'),
      onClick: () => {
        setQueryParams(prev => ({
          ...prev,
          status: undefined,
          page: 1,
        }));
      },
    },
    ...Object.values(DataFineTuneStatus).map(status => ({
      id: status,
      label: getStatusLabel(status),
      onClick: () => {
        setQueryParams(prev => ({
          ...prev,
          status,
          page: 1,
        }));
      },
    })),
  ];

  // Loading state
  if (isLoading) {
    return (
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <Typography variant="h4" className="flex items-center">
              <Database className="mr-2 text-primary" size={24} />
              {t('Dataset Fine-tune')}
            </Typography>
            <Typography variant="body2" className="text-gray-600 mt-1">
              {t('Đang tải...')}
            </Typography>
          </div>
        </div>

        {/* MenuIconBar */}
        <MenuIconBar
          onSearch={handleSearch}
          onAdd={handleCreateNew}
          items={filterMenuItems}
          showDateFilter={false}
          showColumnFilter={false}
          isLoading={isLoading}
        />

        {/* Loading */}
        <div className="flex justify-center items-center h-64">
          <Loading size="lg" />
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <Typography variant="h4" className="flex items-center">
              <Database className="mr-2 text-primary" size={24} />
              {t('Dataset Fine-tune')}
            </Typography>
            <Typography variant="body2" className="text-gray-600 mt-1">
              {t('Có lỗi xảy ra')}
            </Typography>
          </div>
        </div>

        {/* Error */}
        <div className="text-center py-12">
          <Typography variant="body1" className="text-red-600 mb-4">
            {t('Có lỗi xảy ra khi tải dữ liệu')}
          </Typography>
          <Button variant="outline" onClick={() => window.location.reload()}>
            {t('Thử lại')}
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <Typography variant="h4" className="flex items-center">
            <Database className="mr-2 text-primary" size={24} />
            {t('Dataset Fine-tune')}
          </Typography>
          <Typography variant="body2" className="text-gray-600 mt-1">
            {data?.total ? `${data.total} datasets` : t('Chưa có dataset nào')}
          </Typography>
        </div>
      </div>

      {/* MenuIconBar */}
      <MenuIconBar
        onSearch={handleSearch}
        onAdd={handleCreateNew}
        items={filterMenuItems}
        showDateFilter={false}
        showColumnFilter={false}
        isLoading={isLoading}
      />

      {/* Dataset Cards */}
      {data?.data && data.data.length > 0 ? (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {data.data.map(dataset => (
              <DatasetCard
                key={dataset.id}
                dataset={dataset}
                onClick={() => handleSelectDataset(dataset.id)}
              />
            ))}
          </div>

          {/* Pagination */}
          {data.totalPages > 1 && (
            <div className="flex justify-center mt-8">
              <Pagination
                variant="simple"
                currentPage={data.page}
                totalPages={data.totalPages}
                onPageChange={handlePageChange}
                showItemsPerPageSelector={false}
              />
            </div>
          )}
        </>
      ) : (
        <div className="text-center py-12">
          <Typography variant="body1" color="muted" className="text-lg">
            {t('Không tìm thấy dataset nào')}
          </Typography>
        </div>
      )}
    </div>
  );
};

export default DataFineTunePage;
