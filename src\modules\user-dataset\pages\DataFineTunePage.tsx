import React, { useState } from 'react';
import {
  Typography,
  Button,
  Input,
  Select,
  Pagination,
  Loading,
  Empty,
} from '@/shared/components/common';
import { useTranslation } from 'react-i18next';
import { Search, Plus, Database } from 'lucide-react';
import { useUserDataFineTuneList } from '../user-data-fine-tune/hooks/useUserDataFineTune';
import { DatasetCard } from '../user-data-fine-tune/components';
import {
  DataFineTuneStatus,
  UserDataFineTuneQueryDto,
  UserDataFineTuneSortBy,
} from '../user-data-fine-tune/types/user-data-fine-tune.types';

/**
 * Data Fine-tune Page - Hiển thị danh sách dataset fine-tune
 */
const DataFineTunePage: React.FC = () => {
  const { t } = useTranslation();
  const [queryParams, setQueryParams] = useState<UserDataFineTuneQueryDto>({
    page: 1,
    limit: 12,
    search: '',
    status: undefined,
    sortBy: UserDataFineTuneSortBy.CREATED_AT,
    sortDirection: 'DESC',
  });

  const { data, isLoading, error } = useUserDataFineTuneList(queryParams);

  // Status labels
  const getStatusLabel = (status: DataFineTuneStatus) => {
    switch (status) {
      case DataFineTuneStatus.PENDING:
        return t('Đang chờ');
      case DataFineTuneStatus.PROCESSING:
        return t('Đang xử lý');
      case DataFineTuneStatus.COMPLETED:
        return t('Hoàn thành');
      case DataFineTuneStatus.FAILED:
        return t('Thất bại');
      case DataFineTuneStatus.CANCELLED:
        return t('Đã hủy');
      default:
        return status;
    }
  };

  // Handle search
  const handleSearch = (value: string) => {
    setQueryParams(prev => ({
      ...prev,
      search: value,
      page: 1,
    }));
  };

  // Handle filter
  const handleStatusFilter = (status: DataFineTuneStatus | undefined) => {
    setQueryParams(prev => ({
      ...prev,
      status,
      page: 1,
    }));
  };

  // Handle pagination
  const handlePageChange = (page: number) => {
    setQueryParams(prev => ({
      ...prev,
      page,
    }));
  };

  const handleCreateNew = () => {
    // Navigate to create dataset page (sẽ tạo sau)
    console.log('Navigate to create dataset');
  };

  const handleSelectDataset = (id: string) => {
    // Navigate to dataset detail page (sẽ tạo sau)
    console.log('Navigate to dataset detail:', id);
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loading size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <Typography variant="body1" className="text-red-600 mb-4">
          {t('Có lỗi xảy ra khi tải dữ liệu')}
        </Typography>
        <Button variant="outline" onClick={() => window.location.reload()}>
          {t('Thử lại')}
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <Typography variant="h4" className="flex items-center">
            <Database className="mr-2 text-primary" size={24} />
            {t('Dataset Fine-tune')}
          </Typography>
          <Typography variant="body2" className="text-gray-600 mt-1">
            {data?.total ? `${data.total} datasets` : t('Chưa có dataset nào')}
          </Typography>
        </div>

        <Button onClick={handleCreateNew} className="flex items-center">
          <Plus size={16} className="mr-2" />
          {t('Tạo Dataset Mới')}
        </Button>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <Input
            placeholder={t('Tìm kiếm dataset...')}
            value={queryParams.search || ''}
            onChange={e => handleSearch(e.target.value)}
            prefix={<Search size={16} className="text-gray-400" />}
            className="w-full"
          />
        </div>

        <Select
          placeholder={t('Tất cả trạng thái')}
          value={queryParams.status || ''}
          onChange={value => handleStatusFilter((value as DataFineTuneStatus) || undefined)}
          className="w-48"
        >
          <Select.Option value="">{t('Tất cả trạng thái')}</Select.Option>
          {Object.values(DataFineTuneStatus).map(status => (
            <Select.Option key={status} value={status}>
              {getStatusLabel(status)}
            </Select.Option>
          ))}
        </Select>
      </div>

      {/* Dataset Cards */}
      {data?.data && data.data.length > 0 ? (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {data.data.map(dataset => (
              <DatasetCard
                key={dataset.id}
                dataset={dataset}
                onClick={() => handleSelectDataset(dataset.id)}
                onEdit={() => console.log('Edit dataset:', dataset.id)}
                onDelete={() => console.log('Delete dataset:', dataset.id)}
                onDownload={() => console.log('Download dataset:', dataset.id)}
                onDuplicate={() => console.log('Duplicate dataset:', dataset.id)}
              />
            ))}
          </div>

          {/* Pagination */}
          {data.totalPages > 1 && (
            <div className="flex justify-center mt-8">
              <Pagination
                current={data.page}
                total={data.total}
                pageSize={data.limit}
                onChange={handlePageChange}
                showSizeChanger={false}
                showQuickJumper
                showTotal={(total, range) => `${range[0]}-${range[1]} của ${total} datasets`}
              />
            </div>
          )}
        </>
      ) : (
        <Empty
          description={
            queryParams.search || queryParams.status
              ? t('Không tìm thấy dataset nào phù hợp')
              : t('Chưa có dataset nào. Tạo dataset đầu tiên của bạn!')
          }
          action={
            !queryParams.search && !queryParams.status ? (
              <Button onClick={handleCreateNew}>{t('Tạo Dataset Đầu Tiên')}</Button>
            ) : undefined
          }
        />
      )}
    </div>
  );
};

export default DataFineTunePage;
