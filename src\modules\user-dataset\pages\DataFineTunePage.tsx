import React from 'react';
import { useNavigate } from 'react-router-dom';
import { DatasetList } from '../user-data-fine-tune/components';

/**
 * Data Fine-tune Page - Hiển thị danh sách dataset fine-tune
 */
const DataFineTunePage: React.FC = () => {
  const navigate = useNavigate();

  const handleCreateNew = () => {
    // Navigate to create dataset page (sẽ tạo sau)
    console.log('Navigate to create dataset');
  };

  const handleSelectDataset = (id: string) => {
    // Navigate to dataset detail page (sẽ tạo sau)
    console.log('Navigate to dataset detail:', id);
  };

  return (
    <div>
      <DatasetList
        onCreateNew={handleCreateNew}
        onSelectDataset={handleSelectDataset}
      />
    </div>
  );
};

export default DataFineTunePage;
