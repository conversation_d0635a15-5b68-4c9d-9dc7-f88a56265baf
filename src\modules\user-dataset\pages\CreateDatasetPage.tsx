import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Typography,
  Button,
  Form,
  FormItem,
  Input,
  Textarea,
  Select,
  Breadcrumb,
} from '@/shared/components/common';
import {
  ArrowLeft,
  Database,
  Upload,
  FileText,
  Settings,
} from 'lucide-react';
import {
  CreateUserDataFineTuneSchema,
  CreateUserDataFineTuneFormData,
} from '../user-data-fine-tune/schemas/user-data-fine-tune.schemas';
import { useCreateUserDataFineTune } from '../user-data-fine-tune/hooks/useUserDataFineTune';
import { ProviderFineTuneEnum } from '../user-data-fine-tune/types/user-data-fine-tune.types';

/**
 * Create Dataset Page - Tạo dataset mới
 */
const CreateDatasetPage: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const createDataset = useCreateUserDataFineTune();
  const [step, setStep] = useState(1);

  const handleSubmit = async (data: CreateUserDataFineTuneFormData) => {
    try {
      const result = await createDataset.mutateAsync(data);
      toast.success(t('Tạo dataset thành công!'));
      navigate(`/user-dataset/${result.id}`);
    } catch (error) {
      toast.error(t('Có lỗi xảy ra khi tạo dataset'));
      console.error('Create dataset error:', error);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Breadcrumb */}
      <Breadcrumb className="mb-6">
        <Breadcrumb.Item onClick={() => navigate('/user-dataset')}>
          {t('Dataset')}
        </Breadcrumb.Item>
        <Breadcrumb.Item>{t('Tạo mới')}</Breadcrumb.Item>
      </Breadcrumb>

      {/* Header */}
      <div className="flex items-center mb-8">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => navigate('/user-dataset')}
          className="mr-4"
        >
          <ArrowLeft size={16} />
        </Button>
        <div>
          <Typography variant="h3" className="flex items-center">
            <Database className="mr-3 text-primary" size={28} />
            {t('Tạo Dataset Fine-tune')}
          </Typography>
          <Typography variant="body1" className="text-gray-600 mt-2">
            {t('Tạo dataset mới để huấn luyện model AI')}
          </Typography>
        </div>
      </div>

      {/* Steps */}
      <div className="flex items-center justify-center mb-8">
        <div className="flex items-center space-x-4">
          <div className={`flex items-center justify-center w-8 h-8 rounded-full ${
            step >= 1 ? 'bg-primary text-white' : 'bg-gray-200 text-gray-600'
          }`}>
            1
          </div>
          <div className={`w-16 h-1 ${step >= 2 ? 'bg-primary' : 'bg-gray-200'}`}></div>
          <div className={`flex items-center justify-center w-8 h-8 rounded-full ${
            step >= 2 ? 'bg-primary text-white' : 'bg-gray-200 text-gray-600'
          }`}>
            2
          </div>
          <div className={`w-16 h-1 ${step >= 3 ? 'bg-primary' : 'bg-gray-200'}`}></div>
          <div className={`flex items-center justify-center w-8 h-8 rounded-full ${
            step >= 3 ? 'bg-primary text-white' : 'bg-gray-200 text-gray-600'
          }`}>
            3
          </div>
        </div>
      </div>

      <div className="flex items-center justify-center mb-4">
        <div className="flex space-x-8 text-sm">
          <span className={step >= 1 ? 'text-primary font-medium' : 'text-gray-500'}>
            {t('Thông tin cơ bản')}
          </span>
          <span className={step >= 2 ? 'text-primary font-medium' : 'text-gray-500'}>
            {t('Upload dữ liệu')}
          </span>
          <span className={step >= 3 ? 'text-primary font-medium' : 'text-gray-500'}>
            {t('Xác nhận')}
          </span>
        </div>
      </div>

      {/* Form */}
      <div className="max-w-2xl mx-auto">
        <Card>
          <div className="p-8">
            <Form
              schema={CreateUserDataFineTuneSchema}
              onSubmit={handleSubmit}
              defaultValues={{
                name: '',
                description: '',
                provider: ProviderFineTuneEnum.OPENAI,
                trainDataset: '',
                validDataset: '',
              }}
              className="space-y-6"
            >
              {/* Step 1: Basic Info */}
              {step === 1 && (
                <div className="space-y-6">
                  <div className="text-center mb-6">
                    <FileText className="mx-auto text-primary mb-4" size={48} />
                    <Typography variant="h5" className="mb-2">
                      {t('Thông tin cơ bản')}
                    </Typography>
                    <Typography variant="body2" className="text-gray-600">
                      {t('Nhập thông tin cơ bản cho dataset của bạn')}
                    </Typography>
                  </div>

                  <FormItem name="name" label={t('Tên Dataset')} required>
                    <Input
                      placeholder={t('Nhập tên dataset')}
                      fullWidth
                    />
                  </FormItem>

                  <FormItem name="description" label={t('Mô tả')}>
                    <Textarea
                      placeholder={t('Mô tả về dataset này...')}
                      rows={4}
                      fullWidth
                    />
                  </FormItem>

                  <FormItem name="provider" label={t('Nhà cung cấp')} required>
                    <Select fullWidth>
                      <Select.Option value={ProviderFineTuneEnum.OPENAI}>
                        OpenAI
                      </Select.Option>
                      <Select.Option value={ProviderFineTuneEnum.ANTHROPIC}>
                        Anthropic
                      </Select.Option>
                      <Select.Option value={ProviderFineTuneEnum.GOOGLE}>
                        Google
                      </Select.Option>
                    </Select>
                  </FormItem>

                  <div className="flex justify-end">
                    <Button
                      type="button"
                      onClick={() => setStep(2)}
                    >
                      {t('Tiếp theo')}
                    </Button>
                  </div>
                </div>
              )}

              {/* Step 2: Upload Data */}
              {step === 2 && (
                <div className="space-y-6">
                  <div className="text-center mb-6">
                    <Upload className="mx-auto text-primary mb-4" size={48} />
                    <Typography variant="h5" className="mb-2">
                      {t('Upload dữ liệu')}
                    </Typography>
                    <Typography variant="body2" className="text-gray-600">
                      {t('Upload file JSONL chứa dữ liệu training và validation')}
                    </Typography>
                  </div>

                  <FormItem name="trainDataset" label={t('Training Dataset')} required>
                    <Textarea
                      placeholder={t('Paste JSONL data hoặc upload file...')}
                      rows={8}
                      fullWidth
                    />
                  </FormItem>

                  <FormItem name="validDataset" label={t('Validation Dataset (Tùy chọn)')}>
                    <Textarea
                      placeholder={t('Paste JSONL data hoặc upload file...')}
                      rows={6}
                      fullWidth
                    />
                  </FormItem>

                  <div className="flex justify-between">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setStep(1)}
                    >
                      {t('Quay lại')}
                    </Button>
                    <Button
                      type="button"
                      onClick={() => setStep(3)}
                    >
                      {t('Tiếp theo')}
                    </Button>
                  </div>
                </div>
              )}

              {/* Step 3: Confirm */}
              {step === 3 && (
                <div className="space-y-6">
                  <div className="text-center mb-6">
                    <Settings className="mx-auto text-primary mb-4" size={48} />
                    <Typography variant="h5" className="mb-2">
                      {t('Xác nhận')}
                    </Typography>
                    <Typography variant="body2" className="text-gray-600">
                      {t('Kiểm tra lại thông tin và tạo dataset')}
                    </Typography>
                  </div>

                  <div className="bg-gray-50 p-6 rounded-lg space-y-4">
                    <Typography variant="h6">{t('Thông tin dataset')}</Typography>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-600">{t('Tên')}:</span>
                        <span className="ml-2 font-medium">Dataset Name</span>
                      </div>
                      <div>
                        <span className="text-gray-600">{t('Provider')}:</span>
                        <span className="ml-2 font-medium">OpenAI</span>
                      </div>
                    </div>
                  </div>

                  <div className="flex justify-between">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setStep(2)}
                    >
                      {t('Quay lại')}
                    </Button>
                    <Button
                      type="submit"
                      isLoading={createDataset.isPending}
                      className="flex items-center"
                    >
                      <Database size={16} className="mr-2" />
                      {t('Tạo Dataset')}
                    </Button>
                  </div>
                </div>
              )}
            </Form>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default CreateDatasetPage;
