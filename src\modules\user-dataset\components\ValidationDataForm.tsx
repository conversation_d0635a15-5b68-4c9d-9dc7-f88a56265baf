import React, { useState, useEffect } from 'react';
import { Card, Input, Button, FormItem, Textarea } from '@/shared/components/common';
import { useTranslation } from 'react-i18next';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  CreateUserDataFineTuneDto,
  ProviderFineTuneEnum,
} from '../user-data-fine-tune/types/user-data-fine-tune.types';
import { useCreateAndUploadDataset } from '../user-data-fine-tune/hooks/useUserDataFineTune';
import {
  convertConversationsToJsonl,
  validateJsonlData,
} from '../user-data-fine-tune/services/user-data-fine-tune.service';
import ChatLayout from './ChatLayout';

// Schema validation cho Validation dataset form
const ValidationDataFormSchema = z.object({
  name: z.string().min(1, 'Dataset name is required'),
  description: z.string().min(1, 'Dataset description is required'),
});

type ValidationDataFormData = z.infer<typeof ValidationDataFormSchema>;

// Temporary type for ImportedConversation
interface ImportedConversation {
  id: string;
  title: string;
  messages: Array<{
    role: 'system' | 'user' | 'assistant';
    content: string;
  }>;
  createdAt: Date;
}

interface ValidationDataFormProps {
  /**
   * Callback khi tạo validation dataset thành công
   */
  onSuccess?: () => void;

  /**
   * Callback khi conversations thay đổi
   */
  onConversationsChange?: (conversations: ImportedConversation[]) => void;

  /**
   * Initial conversations từ localStorage
   */
  initialConversations?: ImportedConversation[];

  /**
   * Control form hiển thị từ parent
   */
  showForm?: boolean;

  /**
   * Callback khi showForm thay đổi
   */
  onShowFormChange?: (show: boolean) => void;
}

/**
 * Component form tạo validation dataset với ChatLayout
 */
const ValidationDataForm: React.FC<ValidationDataFormProps> = ({
  onSuccess,
  onConversationsChange,
  initialConversations = [],
  showForm: externalShowForm = false,
  onShowFormChange,
}) => {
  const { t } = useTranslation();
  const { createAndUpload, isLoading } = useCreateAndUploadDataset();

  // State cho conversations và dataset với initial value từ localStorage
  const [conversations, setConversations] = useState<ImportedConversation[]>(initialConversations);

  // Use external showForm if provided, otherwise use internal state
  const [internalShowForm, setInternalShowForm] = useState(false);
  const showForm = onShowFormChange ? externalShowForm : internalShowForm;
  const setShowForm = onShowFormChange || setInternalShowForm;

  // Sync conversations khi initialConversations thay đổi (khi chuyển tab)
  useEffect(() => {
    setConversations(initialConversations);
  }, [initialConversations]);

  // Khởi tạo form với React Hook Form và Zod
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<ValidationDataFormData>({
    resolver: zodResolver(ValidationDataFormSchema),
    defaultValues: {
      name: '',
      description: '',
    },
  });

  // Handle conversations change từ ChatLayout
  const handleConversationsChange = (updatedConversations: ImportedConversation[]) => {
    setConversations(updatedConversations);
    // Notify parent component
    if (onConversationsChange) {
      onConversationsChange(updatedConversations);
    }
  };

  // Convert conversations to validation data for submission
  const convertConversationsToValidationDataset = () => {
    if (conversations.length === 0) {
      return '';
    }
    return convertConversationsToJsonl(conversations);
  };

  // Xử lý submit form
  const onSubmit = async (data: ValidationDataFormData) => {
    try {
      if (conversations.length === 0) {
        console.error('No validation conversations available');
        return;
      }

      // Convert conversations to JSONL format
      const validJsonlData = convertConversationsToValidationDataset();

      // Validate JSONL data
      const validation = validateJsonlData(validJsonlData);
      if (!validation.isValid) {
        console.error('Validation data validation failed:', validation.errors);
        return;
      }

      // Create dataset info for OpenAI Validation
      const datasetInfo: CreateUserDataFineTuneDto = {
        name: `${data.name}_validation`,
        description: `${data.description} (Validation Dataset)`,
        provider: ProviderFineTuneEnum.OPENAI,
        trainDataset: 'application/jsonl', // Dummy training data
        validDataset: 'application/jsonl', // Main validation data
      };

      // Create and upload dataset (với validation data làm chính)
      await createAndUpload({
        datasetInfo,
        trainJsonlData: '{"messages":[{"role":"system","content":"Validation dataset"}]}', // Dummy
        validJsonlData,
      });

      // Reset form và notify success
      reset();
      setConversations([]);
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error('Error creating validation dataset:', error);
    }
  };

  return (
    <div className="h-full flex flex-col">
      {/* Form Header */}
      {showForm && (
        <div className="flex-shrink-0 p-4 border-b border-gray-200 dark:border-gray-700">
          <Card>
            <form onSubmit={handleSubmit(onSubmit)}>
              <div className="grid grid-cols-1 gap-4 mb-4">
                <FormItem name="name" label={t('Name')} helpText={errors.name?.message} required>
                  <Input
                    {...register('name')}
                    placeholder={t('Nhập tên validation dataset')}
                    error={errors.name?.message as string}
                    fullWidth
                  />
                </FormItem>

                <FormItem
                  name="description"
                  label={t('Description')}
                  helpText={errors.description?.message}
                  required
                >
                  <Textarea
                    {...register('description')}
                    placeholder={t('Nhập mô tả validation dataset')}
                    status={errors.description?.message ? 'error' : 'default'}
                    rows={3}
                    fullWidth
                  />
                </FormItem>
              </div>

              <div className="flex justify-between items-center">
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  {conversations.length > 0 && (
                    <span>{conversations.length} validation conversations</span>
                  )}
                </div>

                <div className="flex space-x-2">
                  <Button type="button" variant="outline" onClick={() => setShowForm(false)}>
                    {t('Cancel')}
                  </Button>
                  <Button
                    type="submit"
                    isLoading={isLoading}
                    disabled={conversations.length === 0}
                    className="bg-orange-600 hover:bg-orange-700"
                  >
                    {t('Tạo Validation Dataset')}
                  </Button>
                </div>
              </div>
            </form>
          </Card>
        </div>
      )}

      {/* Chat Layout Container */}
      <div className="flex-1 relative">
        <div className="h-full">
          <ChatLayout onConversationsChange={handleConversationsChange} />
        </div>

        {/* Create Dataset Button */}
        {conversations.length > 0 && !showForm && (
          <div className="absolute top-4 right-4 z-10">
            <Button
              onClick={() => setShowForm(true)}
              size="sm"
              className="bg-orange-600 hover:bg-orange-700"
            >
              {t('Tạo Validation Dataset')} ({conversations.length})
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default ValidationDataForm;
