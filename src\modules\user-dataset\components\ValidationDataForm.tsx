import React, { useState, useEffect } from 'react';
import { Card, Input, Button, FormItem, Textarea } from '@/shared/components/common';
import { useTranslation } from 'react-i18next';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  CreateUserDataFineTuneDto,
  ProviderFineTuneEnum,
  ImportedConversation,
} from '../user-data-fine-tune/types/user-data-fine-tune.types';
import { useCreateAndUploadDataset } from '../user-data-fine-tune/hooks/useUserDataFineTune';
import {
  convertConversationsToJsonl,
  validateJsonlData,
} from '../user-data-fine-tune/services/user-data-fine-tune.service';
import ChatLayout from './ChatLayout';

// Schema validation cho OpenAI validation dataset form
const ValidationDatasetFormSchema = z.object({
  name: z.string().min(1, 'Dataset name is required'),
  description: z.string().min(1, 'Dataset description is required'),
});

type ValidationDatasetFormData = z.infer<typeof ValidationDatasetFormSchema>;

interface ValidationDataFormProps {
  /**
   * Callback khi tạo validation dataset thành công
   */
  onSuccess?: () => void;

  /**
   * Callback khi conversations thay đổi
   */
  onConversationsChange?: (conversations: ImportedConversation[]) => void;

  /**
   * Initial conversations từ localStorage
   */
  initialConversations?: ImportedConversation[];

  /**
   * Control form hiển thị từ parent
   */
  showForm?: boolean;

  /**
   * Callback khi showForm thay đổi
   */
  onShowFormChange?: (show: boolean) => void;
}

/**
 * Component form tạo validation dataset với ChatLayout cho OpenAI
 */
const ValidationDataForm: React.FC<ValidationDataFormProps> = ({
  onSuccess,
  onConversationsChange,
  initialConversations = [],
  showForm: externalShowForm = false,
  onShowFormChange,
}) => {
  const { t } = useTranslation();
  const { createAndUpload, isLoading } = useCreateAndUploadDataset();

  // State cho conversations và dataset với initial value từ localStorage
  const [conversations, setConversations] = useState<ImportedConversation[]>(initialConversations);

  // Use external showForm if provided, otherwise use internal state
  const [internalShowForm, setInternalShowForm] = useState(false);
  const showForm = onShowFormChange ? externalShowForm : internalShowForm;
  const setShowForm = onShowFormChange || setInternalShowForm;

  // Sync conversations khi initialConversations thay đổi (khi chuyển tab)
  useEffect(() => {
    setConversations(initialConversations);
  }, [initialConversations]);

  // Khởi tạo form với React Hook Form và Zod
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<ValidationDatasetFormData>({
    resolver: zodResolver(ValidationDatasetFormSchema),
    defaultValues: {
      name: '',
      description: '',
    },
  });

  // Handle conversations change từ ChatLayout
  const handleConversationsChange = (updatedConversations: ImportedConversation[]) => {
    setConversations(updatedConversations);
    // Notify parent component
    if (onConversationsChange) {
      onConversationsChange(updatedConversations);
    }
  };

  // Xử lý submit form với user-data-fine-tune API
  const onSubmit = async (data: ValidationDatasetFormData) => {
    try {
      if (conversations.length === 0) {
        console.error('No conversations available');
        return;
      }

      // Convert conversations to JSONL format
      const validJsonlData = convertConversationsToJsonl(conversations);

      // Validate JSONL data
      const validation = validateJsonlData(validJsonlData);
      if (!validation.isValid) {
        console.error('Validation data validation failed:', validation.errors);
        return;
      }

      // Create dataset info for OpenAI (validation data only)
      const datasetInfo: CreateUserDataFineTuneDto = {
        name: `${data.name} (Validation)`,
        description: data.description,
        provider: ProviderFineTuneEnum.OPENAI,
        trainDataset: 'application/jsonl',
        validDataset: 'application/jsonl',
      };

      // Create and upload dataset with validation data as both train and valid
      // (Since this is validation-only dataset)
      await createAndUpload({
        datasetInfo,
        trainJsonlData: validJsonlData, // Use validation data as training data
        validJsonlData: validJsonlData, // Also as validation data
      });

      // Reset form và notify success
      reset();
      setConversations([]);
      setShowForm(false);

      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error('Error creating OpenAI validation dataset:', error);
    }
  };

  return (
    <div className="space-y-4">
      {/* Form Header */}
      {showForm && (
        <Card>
          <form onSubmit={handleSubmit(onSubmit)}>
            <div className="grid grid-cols-1 gap-4 mb-4">
              <FormItem name="name" label={t('Name')} helpText={errors.name?.message} required>
                <Input
                  {...register('name')}
                  placeholder={t('Nhập tên validation dataset')}
                  error={errors.name?.message as string}
                  fullWidth
                />
              </FormItem>

              <FormItem
                name="description"
                label={t('Description')}
                helpText={errors.description?.message}
                required
              >
                <Textarea
                  {...register('description')}
                  placeholder={t('Nhập mô tả validation dataset')}
                  status={errors.description?.message ? 'error' : 'default'}
                  rows={3}
                  fullWidth
                />
              </FormItem>
            </div>

            <div className="flex justify-between items-center">
              <div className="text-sm text-gray-600 dark:text-gray-400">
                {conversations.length > 0 && (
                  <span>{conversations.length} conversations • Validation data for OpenAI</span>
                )}
              </div>

              <div className="flex space-x-2">
                <Button type="button" variant="outline" onClick={() => setShowForm(false)}>
                  {t('Cancel')}
                </Button>
                <Button
                  type="submit"
                  isLoading={isLoading}
                  disabled={conversations.length === 0}
                  className="bg-orange-600 hover:bg-orange-700"
                >
                  {t('Tạo Validation Dataset')}
                </Button>
              </div>
            </div>
          </form>
        </Card>
      )}

      {/* Chat Layout Container */}
      <Card className="relative">
        <div className="h-[600px]">
          <ChatLayout onConversationsChange={handleConversationsChange} />
        </div>

        {/* Action Buttons */}
        <div className="absolute top-4 right-4 flex gap-2">
          {/* Create Dataset Button */}
          {conversations.length > 0 && !showForm && (
            <Button
              onClick={() => setShowForm(true)}
              size="sm"
              className="bg-orange-600 hover:bg-orange-700"
            >
              {t('Tạo Validation Dataset')} ({conversations.length})
            </Button>
          )}
        </div>
      </Card>
    </div>
  );
};

export default ValidationDataForm;
