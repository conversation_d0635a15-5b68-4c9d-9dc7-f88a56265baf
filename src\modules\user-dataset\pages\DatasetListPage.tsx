import React from 'react';
import { useTranslation } from 'react-i18next';

import { ModuleCard } from '@/modules/components/card';
import ResponsiveGrid from '@/shared/components/common/ResponsiveGrid/ResponsiveGrid';

/**
 * Trang tổng quan quản lý User Dataset
 */
const UserDatasetManagementPage: React.FC = () => {
  const { t } = useTranslation(['user-dataset']);

  return (
    <div>
      <ResponsiveGrid
        maxColumns={{ xs: 1, sm: 2, md: 2, lg: 3, xl: 3 }}
        maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 1, lg: 2, xl: 3 }}
        gap={6}
      >
        {/* Dataset Fine-tune Card */}
        <ModuleCard
          title={t('user-dataset:dataFineTune.title', 'Dataset Fine-tune')}
          description={t(
            'user-dataset:dataFineTune.description',
            'Quản lý dataset để huấn luyện và fine-tune các model AI.'
          )}
          icon="database"
          linkTo="/user-dataset/data-fine-tune"
        />

        {/* Model Fine-tune Card */}
        <ModuleCard
          title={t('user-dataset:modelFineTune.title', 'Model Fine-tune')}
          description={t(
            'user-dataset:modelFineTune.description',
            'Tạo và quản lý các model đã được fine-tune từ dataset của bạn.'
          )}
          icon="brain"
          linkTo="/user-dataset/model-fine-tune"
        />

        {/* Base Models Card */}
        <ModuleCard
          title={t('user-dataset:modeBase.title', 'Base Models')}
          description={t(
            'user-dataset:modeBase.description',
            'Khám phá và so sánh các base model có sẵn để fine-tune.'
          )}
          icon="settings"
          linkTo="/user-dataset/mode-base"
        />

        {/* API Keys Card */}
        <ModuleCard
          title={t('user-dataset:keyLlm.title', 'API Keys')}
          description={t(
            'user-dataset:keyLlm.description',
            'Quản lý các API key để kết nối với các nhà cung cấp AI.'
          )}
          icon="key"
          linkTo="/user-dataset/key-llm"
        />
      </ResponsiveGrid>
    </div>
  );
};

export default UserDatasetManagementPage;
