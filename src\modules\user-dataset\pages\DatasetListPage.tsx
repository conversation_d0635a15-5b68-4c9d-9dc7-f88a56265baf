import React from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import { useTranslation } from 'react-i18next';
import { DatasetList } from '../user-data-fine-tune/components';
import { useDeleteUserDataFineTune } from '../user-data-fine-tune/hooks/useUserDataFineTune';

/**
 * Dataset List Page - Hiển thị danh sách dataset fine-tune
 */
const DatasetListPage: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const deleteDataset = useDeleteUserDataFineTune();

  const handleCreateNew = () => {
    navigate('/user-dataset/create');
  };

  const handleSelectDataset = (id: string) => {
    navigate(`/user-dataset/${id}`);
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <DatasetList
        onCreateNew={handleCreateNew}
        onSelectDataset={handleSelectDataset}
      />
    </div>
  );
};

export default DatasetListPage;
