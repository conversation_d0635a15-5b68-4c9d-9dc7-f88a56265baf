import { apiClient } from '@/shared/services/api-client';
import {
  CreateUserKeyLlmDto,
  UpdateUserKeyLlmDto,
  UserKeyLlmResponseDto,
  UserKeyLlmDetailResponseDto,
  UserKeyLlmQueryDto,
  TestKeyDto,
  TestKeyResponseDto,
  KeyUsageStatsResponseDto,
  PaginatedResult,
} from '../types/user-key-llm.types';

/**
 * Service cho User Key LLM API
 */
export class UserKeyLlmService {
  private static readonly BASE_URL = '/user-key-llm';

  /**
   * Lấy danh sách key LLM
   */
  static async getList(params: UserKeyLlmQueryDto): Promise<PaginatedResult<UserKeyLlmResponseDto>> {
    const response = await apiClient.get<PaginatedResult<UserKeyLlmResponseDto>>(
      this.BASE_URL,
      { params }
    );
    return response.data;
  }

  /**
   * Lấy chi tiết key LLM
   */
  static async getById(id: string): Promise<UserKeyLlmDetailResponseDto> {
    const response = await apiClient.get<UserKeyLlmDetailResponseDto>(`${this.BASE_URL}/${id}`);
    return response.data;
  }

  /**
   * Tạo key LLM mới
   */
  static async create(data: CreateUserKeyLlmDto): Promise<UserKeyLlmResponseDto> {
    const response = await apiClient.post<UserKeyLlmResponseDto>(this.BASE_URL, data);
    return response.data;
  }

  /**
   * Cập nhật key LLM
   */
  static async update(id: string, data: UpdateUserKeyLlmDto): Promise<UserKeyLlmResponseDto> {
    const response = await apiClient.put<UserKeyLlmResponseDto>(`${this.BASE_URL}/${id}`, data);
    return response.data;
  }

  /**
   * Xóa key LLM
   */
  static async delete(id: string): Promise<void> {
    await apiClient.delete(`${this.BASE_URL}/${id}`);
  }

  /**
   * Test key LLM
   */
  static async testKey(id: string, data: TestKeyDto = {}): Promise<TestKeyResponseDto> {
    const response = await apiClient.post<TestKeyResponseDto>(`${this.BASE_URL}/${id}/test`, data);
    return response.data;
  }

  /**
   * Lấy thống kê sử dụng key
   */
  static async getUsageStats(id: string): Promise<KeyUsageStatsResponseDto> {
    const response = await apiClient.get<KeyUsageStatsResponseDto>(`${this.BASE_URL}/${id}/usage-stats`);
    return response.data;
  }

  /**
   * Kích hoạt key
   */
  static async activate(id: string): Promise<UserKeyLlmResponseDto> {
    const response = await apiClient.post<UserKeyLlmResponseDto>(`${this.BASE_URL}/${id}/activate`);
    return response.data;
  }

  /**
   * Vô hiệu hóa key
   */
  static async deactivate(id: string): Promise<UserKeyLlmResponseDto> {
    const response = await apiClient.post<UserKeyLlmResponseDto>(`${this.BASE_URL}/${id}/deactivate`);
    return response.data;
  }

  /**
   * Làm mới key (regenerate)
   */
  static async regenerate(id: string): Promise<UserKeyLlmResponseDto> {
    const response = await apiClient.post<UserKeyLlmResponseDto>(`${this.BASE_URL}/${id}/regenerate`);
    return response.data;
  }

  /**
   * Nhân bản key
   */
  static async duplicate(id: string, name: string): Promise<UserKeyLlmResponseDto> {
    const response = await apiClient.post<UserKeyLlmResponseDto>(`${this.BASE_URL}/${id}/duplicate`, {
      name,
    });
    return response.data;
  }

  /**
   * Lấy danh sách models có sẵn cho provider
   */
  static async getAvailableModels(provider: string): Promise<string[]> {
    const response = await apiClient.get<string[]>(`${this.BASE_URL}/models/${provider}`);
    return response.data;
  }

  /**
   * Validate key format
   */
  static async validateKeyFormat(provider: string, apiKey: string): Promise<{ valid: boolean; message: string }> {
    const response = await apiClient.post<{ valid: boolean; message: string }>(
      `${this.BASE_URL}/validate-format`,
      { provider, apiKey }
    );
    return response.data;
  }

  /**
   * Lấy thông tin quota/billing cho key
   */
  static async getQuotaInfo(id: string): Promise<{
    quota: {
      total: number;
      used: number;
      remaining: number;
    };
    billing: {
      currentPeriod: {
        start: string;
        end: string;
        usage: number;
        cost: number;
      };
    };
  }> {
    const response = await apiClient.get(`${this.BASE_URL}/${id}/quota`);
    return response.data;
  }

  /**
   * Export keys (for backup)
   */
  static async exportKeys(): Promise<Blob> {
    const response = await apiClient.get(`${this.BASE_URL}/export`, {
      responseType: 'blob',
    });
    return response.data;
  }

  /**
   * Import keys (from backup)
   */
  static async importKeys(file: File): Promise<{
    imported: number;
    failed: number;
    errors: string[];
  }> {
    const formData = new FormData();
    formData.append('file', file);

    const response = await apiClient.post(`${this.BASE_URL}/import`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }

  /**
   * Batch operations
   */
  static async batchDelete(ids: string[]): Promise<{
    deleted: number;
    failed: number;
    errors: string[];
  }> {
    const response = await apiClient.post(`${this.BASE_URL}/batch/delete`, { ids });
    return response.data;
  }

  static async batchActivate(ids: string[]): Promise<{
    activated: number;
    failed: number;
    errors: string[];
  }> {
    const response = await apiClient.post(`${this.BASE_URL}/batch/activate`, { ids });
    return response.data;
  }

  static async batchDeactivate(ids: string[]): Promise<{
    deactivated: number;
    failed: number;
    errors: string[];
  }> {
    const response = await apiClient.post(`${this.BASE_URL}/batch/deactivate`, { ids });
    return response.data;
  }
}

export default UserKeyLlmService;
