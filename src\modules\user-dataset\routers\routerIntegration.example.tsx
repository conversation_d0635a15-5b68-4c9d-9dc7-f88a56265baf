import React from 'react';
import { createBrowserRouter, RouterProvider } from 'react-router-dom';
import { userDatasetRoutes } from './userDatasetRoutes';
import { UserDatasetLayout } from '../components';

/**
 * Example: <PERSON><PERSON><PERSON> tích hợp user-dataset routes vào main router
 */

// Option 1: Sử dụng với Layout
const routesWithLayout = [
  {
    path: '/user-dataset',
    element: <UserDatasetLayout />,
    children: [
      {
        index: true,
        element: <DatasetListPage />,
      },
      {
        path: 'create',
        element: <CreateDatasetPage />,
      },
      {
        path: ':id',
        element: <DatasetDetailPage />,
      },
      {
        path: ':id/edit',
        element: <CreateDatasetPage />,
      },
    ],
  },
];

// Option 2: Tích hợp vào main router
const mainRouter = createBrowserRouter([
  {
    path: '/',
    element: <MainLayout />,
    children: [
      // Other routes...
      ...userDatasetRoutes,
    ],
  },
]);

// Option 3: Lazy loading
const LazyUserDatasetRoutes = React.lazy(() => 
  import('./userDatasetRoutes').then(module => ({ 
    default: () => <RouterProvider router={createBrowserRouter(module.userDatasetRoutes)} />
  }))
);

/**
 * Usage trong main App.tsx:
 * 
 * import { userDatasetRoutes } from '@/modules/user-dataset';
 * 
 * const router = createBrowserRouter([
 *   {
 *     path: '/',
 *     element: <MainLayout />,
 *     children: [
 *       ...userDatasetRoutes,
 *       // other routes...
 *     ],
 *   },
 * ]);
 */

export { routesWithLayout, mainRouter, LazyUserDatasetRoutes };
