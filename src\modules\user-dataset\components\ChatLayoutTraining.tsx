/* eslint-disable prefer-const */
import React, { useState, useCallback } from 'react';
import {
  ImportedConversation,
  DatasetMessage,
} from '../user-data-fine-tune/types/user-data-fine-tune.types';
import ConversationSidebar from './ConversationSidebar';
import ChatPanelWithRoleLogic from './ChatPanelWithRoleLogic';
import { useTranslation } from 'react-i18next';

interface ChatLayoutTrainingProps {
  /**
   * Callback khi có thay đổi conversations
   */
  onConversationsChange?: (conversations: ImportedConversation[]) => void;
}

/**
 * Component layout cho Training Data chat interface
 * Role Selection Logic:
 * - Message 1: SystemRole (bắt buộc)
 * - Message 2: UserRole (bắt buộc)
 * - Message 3+: UserRole/AssistantRole (cho chọn, không có SystemRole)
 */
const ChatLayoutTraining: React.FC<ChatLayoutTrainingProps> = ({ onConversationsChange }) => {
  const { t } = useTranslation();

  // State - riêng cho Training Data
  const [conversations, setConversations] = useState<ImportedConversation[]>([]);
  const [selectedConversationId, setSelectedConversationId] = useState<string | null>(null);
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);

  // Get selected conversation
  const selectedConversation = conversations.find(conv => conv.id === selectedConversationId);

  // Generate unique ID
  const generateId = (): string => {
    return Date.now().toString(36) + Math.random().toString(36).substring(2);
  };

  // Generate conversation title from first user message
  const generateTitle = (messages: DatasetMessage[]): string => {
    const firstUserMessage = messages.find(msg => msg.role === 'user');
    if (firstUserMessage) {
      // Lấy 30 ký tự đầu để title ngắn gọn hơn
      return firstUserMessage.content.length > 30
        ? firstUserMessage.content.substring(0, 30) + '...'
        : firstUserMessage.content;
    }
    return 'Training Conversation';
  };

  // Handle import file
  const handleImportFile = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const file = event.target.files?.[0];
      if (!file) return;

      const reader = new FileReader();
      reader.onload = e => {
        try {
          const content = e.target?.result as string;
          const isJsonl = file.name.toLowerCase().endsWith('.jsonl');

          if (isJsonl) {
            // Parse JSONL format
            const lines = content.trim().split('\n');

            const newConversations: ImportedConversation[] = [];

            lines.forEach((line, index) => {
              try {
                const trimmedLine = line.trim();
                if (trimmedLine) {
                  const jsonObj = JSON.parse(trimmedLine);
                  if (jsonObj.messages && Array.isArray(jsonObj.messages)) {
                    const conversation: ImportedConversation = {
                      id: generateId(),
                      title: generateTitle(jsonObj.messages),
                      messages: jsonObj.messages,
                      createdAt: new Date(),
                    };
                    newConversations.push(conversation);
                  } else {
                    console.warn(`⚠️ [Training] Line ${index + 1} missing messages array`);
                  }
                }
              } catch (lineError) {
                console.error(`❌ [Training] Error parsing line ${index + 1}:`, lineError);
              }
            });

            if (newConversations.length > 0) {
              setConversations(prev => {
                const updated = [...prev, ...newConversations];
                // Notify parent with updated state
                onConversationsChange?.(updated);
                return updated;
              });

              // Auto select first imported conversation
              setSelectedConversationId(newConversations[0].id);
            } else {
              console.warn('⚠️ [Training] No conversations were imported');
            }
          } else {
            // Parse JSON format (legacy)
            const json = JSON.parse(content);
            if (json.conversations && Array.isArray(json.conversations)) {
              const newConversations: ImportedConversation[] = json.conversations.map(
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                (conv: any) => ({
                  id: generateId(),
                  title: generateTitle(conv.messages),
                  messages: conv.messages,
                  createdAt: new Date(),
                })
              );

              setConversations(prev => {
                const updated = [...prev, ...newConversations];
                // Notify parent with updated state
                onConversationsChange?.(updated);
                return updated;
              });
              if (newConversations.length > 0) {
                setSelectedConversationId(newConversations[0].id);
              }
            }
          }
        } catch (error) {
          console.error('[Training] Error parsing file:', error);
          alert('Error parsing file. Please check the file format.');
        }
      };

      reader.readAsText(file);
      // Reset input
      event.target.value = '';
    },
    [onConversationsChange]
  );

  // Handle select conversation
  const handleSelectConversation = useCallback((conversationId: string) => {
    setSelectedConversationId(conversationId);
  }, []);

  // Handle delete conversation
  const handleDeleteConversation = useCallback(
    (conversationId: string) => {
      setConversations(prev => {
        const updated = prev.filter(conv => conv.id !== conversationId);

        // If deleted conversation was selected, select another one
        if (selectedConversationId === conversationId) {
          setSelectedConversationId(updated.length > 0 ? updated[0].id : null);
        }

        onConversationsChange?.(updated);
        return updated;
      });
    },
    [selectedConversationId, onConversationsChange]
  );

  // Handle toggle sidebar
  const handleToggleSidebar = useCallback(() => {
    setIsSidebarOpen(prev => !prev);
  }, []);

  // Handle new chat
  const handleNewChat = useCallback(() => {
    const newConversation: ImportedConversation = {
      id: generateId(),
      title: 'Training Chat',
      messages: [],
      createdAt: new Date(),
    };

    setConversations(prev => {
      const updated = [newConversation, ...prev];
      onConversationsChange?.(updated);
      return updated;
    });

    setSelectedConversationId(newConversation.id);
  }, [onConversationsChange]);

  // Handle add message to selected conversation with role validation
  const handleAddMessage = useCallback(
    (message: DatasetMessage) => {
      if (!selectedConversationId) return;

      setConversations(prev => {
        const updated = prev.map(conv => {
          if (conv.id === selectedConversationId) {
            const currentMessages = conv.messages;
            const messageIndex = currentMessages.length;

            // Role validation logic
            let validatedMessage = { ...message };

            if (messageIndex === 0) {
              // Message 1: Must be SystemRole
              validatedMessage.role = 'system';
            } else if (messageIndex === 1) {
              // Message 2: Must be UserRole
              validatedMessage.role = 'user';
            }
            // Message 3+: Allow user/assistant (already validated in ChatPanel)

            const newMessages = [...currentMessages, validatedMessage];

            // Update title if this is first user message
            let newTitle = conv.title;
            if (messageIndex === 1 && validatedMessage.role === 'user') {
              newTitle = generateTitle([validatedMessage]);
            }

            return {
              ...conv,
              title: newTitle,
              messages: newMessages,
            };
          }
          return conv;
        });

        onConversationsChange?.(updated);
        return updated;
      });
    },
    [selectedConversationId, onConversationsChange]
  );

  // Handle delete message from selected conversation
  const handleDeleteMessage = useCallback(
    (messageIndex: number) => {
      if (!selectedConversationId) return;

      setConversations(prev => {
        const updated = prev.map(conv => {
          if (conv.id === selectedConversationId) {
            const newMessages = [...conv.messages];
            newMessages.splice(messageIndex, 1);
            return {
              ...conv,
              messages: newMessages,
            };
          }
          return conv;
        });

        onConversationsChange?.(updated);
        return updated;
      });
    },
    [selectedConversationId, onConversationsChange]
  );

  // Handle edit message in selected conversation
  const handleEditMessage = useCallback(
    (messageIndex: number, message: DatasetMessage) => {
      if (!selectedConversationId) return;

      setConversations(prev => {
        const updated = prev.map(conv => {
          if (conv.id === selectedConversationId) {
            const newMessages = [...conv.messages];

            // Role validation for editing
            let validatedMessage = { ...message };

            if (messageIndex === 0) {
              // Message 1: Must be SystemRole
              validatedMessage.role = 'system';
            } else if (messageIndex === 1) {
              // Message 2: Must be UserRole
              validatedMessage.role = 'user';
            }
            // Message 3+: Allow user/assistant (already validated in ChatPanel)

            newMessages[messageIndex] = validatedMessage;
            return {
              ...conv,
              messages: newMessages,
            };
          }
          return conv;
        });

        onConversationsChange?.(updated);
        return updated;
      });
    },
    [selectedConversationId, onConversationsChange]
  );

  return (
    <div className="h-full flex relative">
      {/* Sidebar */}
      <div
        className={`transition-all duration-300 flex-shrink-0 ${isSidebarOpen ? 'w-80' : 'w-0'}`}
      >
        <ConversationSidebar
          conversations={conversations}
          selectedConversationId={selectedConversationId}
          onSelectConversation={handleSelectConversation}
          onDeleteConversation={handleDeleteConversation}
          onImportFile={handleImportFile}
          onNewChat={handleNewChat}
          isOpen={isSidebarOpen}
          onToggleSidebar={handleToggleSidebar}
        />
      </div>

      {/* Main Content */}
      <div className="flex-1 min-w-0 h-full">
        {selectedConversation ? (
          <div className="h-full w-full">
            <ChatPanelWithRoleLogic
              title={selectedConversation.title}
              messages={selectedConversation.messages}
              onAddMessage={handleAddMessage}
              onDeleteMessage={handleDeleteMessage}
              onEditMessage={handleEditMessage}
              placeholder="Nhập tin nhắn cho Training Data..."
            />
          </div>
        ) : (
          <div className="h-full flex items-center justify-center">
            <div className="text-center">
              <div className="text-4xl mb-3">🎯</div>
              <h3 className="text-lg font-semibold mb-2 text-gray-900 dark:text-white">
                {t('Training Data Chat')}
              </h3>
              <p className="text-gray-500 dark:text-gray-400 text-sm max-w-sm">
                {t('Import file JSONL hoặc tạo conversation mới cho training data')}
              </p>
              <div className="mt-4">
                <div className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400">
                  <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                  Training Data
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ChatLayoutTraining;
