import React, { useState, useRef, useEffect, useMemo } from 'react';
import { Typography, Tooltip, ActionMenu, ActionMenuItem } from '@/shared/components/common';
import { DatasetMessage } from '../user-data-fine-tune/types/user-data-fine-tune.types';
import { Send, User, Bot, Code2 } from 'lucide-react';
import { useTranslation } from 'react-i18next';

interface ChatPanelWithRoleLogicGoogleProps {
  /**
   * Tiêu đề của panel
   */
  title: string;

  /**
   * Danh sách message
   */
  messages: DatasetMessage[];

  /**
   * Callback khi thêm message
   */
  onAddMessage: (message: DatasetMessage) => void;

  /**
   * Callback khi xóa message
   */
  onDeleteMessage: (index: number) => void;

  /**
   * Callback khi chỉnh sửa message
   */
  onEditMessage?: (index: number, message: DatasetMessage) => void;

  /**
   * Placeholder cho input
   */
  placeholder?: string;
}

/**
 * Component hiển thị panel chat với role selection logic cho Google:
 * - <PERSON><PERSON><PERSON> bắ<PERSON> đầu bằng User role (không có System role)
 * - User role: Tối đa 40,000 ký tự
 * - Assistant role: Tối đa 5,000 ký tự
 * - Tin nhắn cuối cùng phải là Assistant
 */
const ChatPanelWithRoleLogicGoogle: React.FC<ChatPanelWithRoleLogicGoogleProps> = ({
  title,
  messages,
  onAddMessage,
  onDeleteMessage,
  onEditMessage,
  placeholder = 'Nhập tin nhắn...',
}) => {
  const { t } = useTranslation();

  // Character limits for Google
  const CHARACTER_LIMITS = {
    user: 40000,      // User role: 40,000 characters
    assistant: 5000,  // Assistant role: 5,000 characters
  };

  // Determine current role based on message count and role logic for Google
  const getCurrentRole = (): 'user' | 'assistant' => {
    const messageCount = messages.length;

    if (messageCount === 0) {
      return 'user'; // Always start with User role (no System role for Google)
    } else {
      // Alternate between user and assistant, but last must be assistant
      const lastMessage = messages[messages.length - 1];
      if (lastMessage.role === 'user') {
        return 'assistant'; // If last was user, next must be assistant
      } else {
        return 'user'; // If last was assistant, next can be user
      }
    }
  };

  // Get available roles based on message count and last message
  const getAvailableRoles = (): ('user' | 'assistant')[] => {
    const messageCount = messages.length;

    if (messageCount === 0) {
      return ['user']; // Always start with User role
    } else {
      // Check last message to determine available roles
      const lastMessage = messages[messages.length - 1];
      if (lastMessage.role === 'user') {
        return ['assistant']; // If last was user, next must be assistant
      } else {
        return ['user', 'assistant']; // If last was assistant, can choose user or assistant
      }
    }
  };

  const [role, setRole] = useState<'user' | 'assistant'>(getCurrentRole());
  const [content, setContent] = useState('');
  const [showRoleMenu, setShowRoleMenu] = useState(false);

  // Update role when messages change
  useEffect(() => {
    const newRole = getCurrentRole();
    setRole(newRole);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [messages.length]);

  // State cho edit message
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const [editingContent, setEditingContent] = useState('');

  // State cho tool selection
  const [showToolSelection, setShowToolSelection] = useState(false);
  const [selectedMessageIndex, setSelectedMessageIndex] = useState<number | null>(null);
  const [messageTools, setMessageTools] = useState<Record<number, string[]>>({});

  // Get available role options based on current message count
  const availableRoles = getAvailableRoles();

  // Danh sách role options (filtered by availability) - Only User and Assistant for Google
  const roleOptions = useMemo(() => {
    const allRoles = [
      {
        value: 'user',
        label: t('User Role'),
        icon: <User size={16} className="mr-2" />,
        color: 'text-blue-500 dark:text-blue-400',
        description: 'User question/input (Max: 40,000 chars)',
      },
      {
        value: 'assistant',
        label: t('Assistant Role'),
        icon: <Bot size={16} className="mr-2" />,
        color: 'text-green-500 dark:text-green-400',
        description: 'AI assistant response (Max: 5,000 chars)',
      },
    ];

    return allRoles.filter(roleOption =>
      availableRoles.includes(roleOption.value as 'user' | 'assistant')
    );
  }, [t, availableRoles]);

  // Get character limit for current role
  const getCurrentCharacterLimit = (): number => {
    return CHARACTER_LIMITS[role];
  };

  // Get remaining characters
  const getRemainingCharacters = (): number => {
    const limit = getCurrentCharacterLimit();
    return Math.max(0, limit - content.length);
  };

  // Check if content exceeds limit
  const isContentTooLong = (): boolean => {
    return content.length > getCurrentCharacterLimit();
  };

  // Get simple tooltip for role selector
  const getRoleTooltip = (): string => {
    const messageCount = messages.length;

    if (messageCount === 0) {
      return 'User Role (Bắt đầu với User)';
    } else {
      return 'Chọn role cho tin nhắn';
    }
  };

  // Xử lý khi gửi message
  const handleSendMessage = () => {
    if (content.trim() && !isContentTooLong()) {
      onAddMessage({
        role,
        content,
      });
      setContent('');

      // Đặt focus lại vào textarea sau khi gửi
      if (textareaRef.current) {
        textareaRef.current.focus();
      }
    }
  };

  // Xử lý khi nhấn Enter
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    // Gửi tin nhắn khi nhấn Enter (không phải Shift+Enter)
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Adjust textarea height based on content
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${Math.min(textareaRef.current.scrollHeight, 150)}px`;
    }
  }, [content]);

  // Đặt focus vào textarea khi component mount
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.focus();
    }
  }, []);

  // Handlers cho edit message
  const handleStartEdit = (index: number, message: DatasetMessage) => {
    setEditingIndex(index);
    setEditingContent(message.content);
  };

  const handleSaveEdit = (index: number) => {
    if (editingContent.trim() && onEditMessage) {
      const messageRole = messages[index].role as 'user' | 'assistant';
      const limit = CHARACTER_LIMITS[messageRole];
      
      if (editingContent.length <= limit) {
        const updatedMessage: DatasetMessage = {
          role: messages[index].role,
          content: editingContent.trim(),
        };
        onEditMessage(index, updatedMessage);
        setEditingIndex(null);
        setEditingContent('');
      }
    }
  };

  const handleCancelEdit = () => {
    setEditingIndex(null);
    setEditingContent('');
  };

  const handleEditKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>, index: number) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSaveEdit(index);
    } else if (e.key === 'Escape') {
      e.preventDefault();
      handleCancelEdit();
    }
  };

  // Mock data cho available tools
  const availableTools = [
    { id: '1', name: 'Get Product Info', description: 'Lấy thông tin sản phẩm' },
    { id: '2', name: 'Get Product Price', description: 'Lấy giá sản phẩm' },
    { id: '3', name: 'Search Products', description: 'Tìm kiếm sản phẩm' },
    { id: '4', name: 'Check Inventory', description: 'Kiểm tra tồn kho' },
    { id: '5', name: 'Calculate Discount', description: 'Tính toán giảm giá' },
  ];

  // Handlers cho tool selection
  const handleShowToolSelection = (index: number) => {
    setSelectedMessageIndex(index);
    setShowToolSelection(true);
  };

  const handleSelectTool = (_toolId: string, toolName: string) => {
    if (selectedMessageIndex !== null) {
      // Chỉ thêm tool vào chip list, không tạo message mới
      const currentTools = messageTools[selectedMessageIndex] || [];
      if (!currentTools.includes(toolName)) {
        setMessageTools(prev => ({
          ...prev,
          [selectedMessageIndex]: [...currentTools, toolName],
        }));
      }
    }

    setShowToolSelection(false);
    setSelectedMessageIndex(null);
  };

  const handleCloseToolSelection = () => {
    setShowToolSelection(false);
    setSelectedMessageIndex(null);
  };

  return (
    <div className="h-full flex flex-col">
      {/* Header - fixed at top */}
      <div className="flex items-center justify-between px-4 pt-4 pb-2 border-b border-gray-200 dark:border-gray-700 flex-shrink-0">
        <Typography variant="h6" className="flex items-center">
          <span className="mr-2">{title}</span>
          <span className="text-sm text-gray-500 dark:text-gray-400">
            (Google Dataset)
          </span>
        </Typography>
      </div>

      {/* Messages area - scrollable */}
      <div className="flex-1 overflow-hidden">
        <div className="h-full overflow-y-auto px-4 py-4 hide-scrollbar">
          {messages.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-64 text-center">
              <div className="text-4xl mb-4">💬</div>
              <h2 className="text-xl font-semibold mb-2">{t('Bắt đầu cuộc trò chuyện')}</h2>
              <p className="text-gray-500 dark:text-gray-400 max-w-md">
                {t('Bắt đầu với User role. User: 40k chars, Assistant: 5k chars')}
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {messages.map((message, index) => {
                // Tạo MessageItem component inline để giữ code ngắn gọn
                const isUser = message.role === 'user';
                const isAssistant = message.role === 'assistant';

                const messageBgColor = isUser
                  ? 'bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 text-gray-900 dark:text-gray-100'
                  : 'bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 text-gray-900 dark:text-gray-100';

                const roleIcon = isUser ? <User size={16} /> : <Bot size={16} />;

                const messageActionItems: ActionMenuItem[] = [
                  {
                    id: 'edit',
                    label: t('Chỉnh sửa'),
                    icon: 'edit',
                    onClick: () => handleStartEdit(index, message),
                  },
                  {
                    id: 'import-tools',
                    label: t('Import Tools JSON'),
                    icon: 'code',
                    onClick: () => handleShowToolSelection(index),
                  },
                  {
                    id: 'delete',
                    label: t('Xóa tin nhắn'),
                    icon: 'trash',
                    onClick: () => onDeleteMessage(index),
                  },
                ];

                return (
                  <div
                    key={index}
                    className={`flex items-start gap-3 mb-4 w-full ${isUser ? 'justify-end' : 'justify-start'}`}
                  >
                    {/* Avatar cho assistant - bên trái */}
                    {isAssistant && (
                      <div className="w-8 h-8 rounded-full overflow-hidden flex-shrink-0 bg-green-100 dark:bg-green-900/30 flex items-center justify-center">
                        <Bot size={18} className="text-green-600 dark:text-green-400" />
                      </div>
                    )}

                    {/* Message content */}
                    <div
                      className={`flex flex-col gap-1 ${isUser ? 'items-end' : 'items-start'} ${isUser ? 'max-w-[70%]' : 'max-w-[70%]'}`}
                    >
                      <div
                        className={`px-4 py-3 rounded-2xl ${messageBgColor} ${isUser ? 'rounded-br-md' : 'rounded-bl-md'}`}
                      >
                        {/* Role indicator với message index và character count */}
                        <div className="flex items-center text-xs font-medium mb-2">
                          <span className="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded-full text-gray-600 dark:text-gray-300 mr-2">
                            #{index + 1}
                          </span>
                          <div
                            className={`flex items-center ${isUser ? 'text-blue-600 dark:text-blue-400' : 'text-green-600 dark:text-green-400'}`}
                          >
                            {roleIcon}
                            <span className="ml-1 capitalize">{message.role}</span>
                            <span className="ml-2 text-gray-500">
                              ({message.content.length}/{isUser ? '40k' : '5k'})
                            </span>
                          </div>
                        </div>

                        {/* Conditional rendering: Edit mode vs Display mode */}
                        {editingIndex === index ? (
                          // Edit mode
                          <div className="space-y-2">
                            <textarea
                              value={editingContent}
                              onChange={e => setEditingContent(e.target.value)}
                              onKeyDown={e => handleEditKeyDown(e, index)}
                              className="w-full p-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-800 dark:text-gray-200 resize-none focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                              rows={3}
                              placeholder="Nhập nội dung tin nhắn..."
                              autoFocus
                            />
                            <div className="flex items-center justify-between">
                              <div className="flex items-center space-x-2">
                                <button
                                  onClick={() => handleSaveEdit(index)}
                                  className="px-3 py-1 text-xs bg-primary text-white rounded-md hover:bg-primary-dark transition-colors"
                                  disabled={!editingContent.trim()}
                                >
                                  Lưu
                                </button>
                                <button
                                  onClick={handleCancelEdit}
                                  className="px-3 py-1 text-xs bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-400 dark:hover:bg-gray-500 transition-colors"
                                >
                                  Hủy
                                </button>
                              </div>
                              <span className="text-xs text-gray-500">
                                {editingContent.length}/{messages[index].role === 'user' ? '40,000' : '5,000'}
                              </span>
                            </div>
                          </div>
                        ) : (
                          // Display mode
                          <div
                            className="whitespace-pre-wrap break-words overflow-hidden"
                            style={{ wordBreak: 'break-word', overflowWrap: 'break-word' }}
                          >
                            {message.content}
                          </div>
                        )}
                      </div>

                      {/* Tool chips - hiển thị tools đã chọn */}
                      {messageTools[index] && messageTools[index].length > 0 && (
                        <div
                          className={`mt-2 flex flex-wrap gap-1 ${isUser ? 'justify-end' : 'justify-start'}`}
                        >
                          {messageTools[index].map((toolName, toolIndex) => (
                            <div
                              key={toolIndex}
                              className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 border border-blue-200 dark:border-blue-700"
                            >
                              <Code2 size={12} className="mr-1" />
                              {toolName}
                            </div>
                          ))}
                        </div>
                      )}

                      <div className={`flex items-center ${isUser ? 'justify-end' : 'justify-start'}`}>
                        <span className="text-xs text-gray-500 dark:text-gray-400">
                          {new Date().toLocaleTimeString()}
                        </span>
                        <div className="ml-2">
                          <ActionMenu
                            items={messageActionItems}
                            menuTooltip={t('Thêm thao tác')}
                            iconSize="sm"
                            iconVariant="default"
                            placement="bottom"
                            menuWidth="180px"
                            showAllInMenu={true}
                            preferRight={true}
                          />
                        </div>
                      </div>
                    </div>

                    {/* Avatar cho user - bên phải */}
                    {isUser && (
                      <div className="w-8 h-8 rounded-full overflow-hidden bg-blue-100 dark:bg-blue-900/30 flex-shrink-0 flex items-center justify-center">
                        <User size={18} className="text-blue-600 dark:text-blue-400" />
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          )}
          <div ref={messagesEndRef} />
        </div>
      </div>

      {/* Input area - fixed at bottom */}
      <div className="border-t border-gray-200 dark:border-gray-700 p-3 flex-shrink-0">
        <div className="relative flex flex-col bg-white dark:bg-gray-800 rounded-xl shadow-lg w-full chat-input-box-container">
          <div className="w-full px-3 py-3">
            <textarea
              ref={textareaRef}
              value={content}
              onChange={e => setContent(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder={t(placeholder)}
              className={`w-full bg-transparent border-0 focus:ring-0 focus:outline-none dark:text-white text-gray-800 resize-none max-h-[150px] custom-scrollbar ${
                isContentTooLong() ? 'text-red-500' : ''
              }`}
              rows={1}
            />
          </div>

          {/* Action buttons row */}
          <div className="flex items-center px-2 py-2 space-x-1 border-t border-gray-100 dark:border-gray-700">
            {/* Role selector button */}
            <div className="relative">
              <Tooltip content={getRoleTooltip()} position="top">
                <button
                  className="p-2 w-10 h-10 flex items-center justify-center text-gray-500 dark:text-gray-400 hover:text-primary dark:hover:text-primary-light hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors flex-shrink-0"
                  onClick={() => setShowRoleMenu(!showRoleMenu)}
                >
                  {role === 'user' ? <User size={18} /> : <Bot size={18} />}
                </button>
              </Tooltip>

              {/* Role selection dropdown */}
              {showRoleMenu && (
                <div className="absolute bottom-full left-0 mb-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-10 min-w-[200px]">
                  {roleOptions.map(option => (
                    <button
                      key={option.value}
                      className={`w-full px-3 py-2 text-left hover:bg-gray-50 dark:hover:bg-gray-700 first:rounded-t-lg last:rounded-b-lg transition-colors ${
                        role === option.value ? 'bg-primary/10 text-primary' : ''
                      }`}
                      onClick={() => {
                        setRole(option.value as 'user' | 'assistant');
                        setShowRoleMenu(false);
                      }}
                    >
                      <div className="flex items-center">
                        <span className={option.color}>{option.icon}</span>
                        <div className="flex-1">
                          <div className="font-medium">{option.label}</div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            {option.description}
                          </div>
                        </div>
                      </div>
                    </button>
                  ))}
                </div>
              )}
            </div>

            {/* Character count and limit */}
            <div className="flex-1 text-center">
              <span
                className={`text-xs ${
                  isContentTooLong()
                    ? 'text-red-500'
                    : getRemainingCharacters() < 100
                    ? 'text-yellow-500'
                    : 'text-gray-500 dark:text-gray-400'
                }`}
              >
                {content.length.toLocaleString()}/{getCurrentCharacterLimit().toLocaleString()}
                {isContentTooLong() && ' (Vượt quá giới hạn!)'}
              </span>
            </div>

            {/* Send button */}
            <Tooltip content={t('Gửi tin nhắn')} position="top">
              <button
                onClick={handleSendMessage}
                disabled={!content.trim() || isContentTooLong()}
                className="p-2 w-10 h-10 flex items-center justify-center text-white bg-primary hover:bg-primary-dark disabled:bg-gray-300 dark:disabled:bg-gray-600 disabled:cursor-not-allowed rounded-full transition-colors flex-shrink-0"
              >
                <Send size={18} />
              </button>
            </Tooltip>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChatPanelWithRoleLogicGoogle;
