import React from 'react';
import { UserDataFineTuneResponseDto, ProviderFineTuneEnum } from '../user-data-fine-tune/types/user-data-fine-tune.types';
import DatasetCard from './DatasetCard';
import { ResponsiveGrid } from '@/shared/components/common';

interface DatasetGridProps {
  datasets: (UserDataFineTuneResponseDto & { provider?: ProviderFineTuneEnum })[];
  onSelectDataset?: (dataset: UserDataFineTuneResponseDto & { provider?: ProviderFineTuneEnum }) => void;
  onViewDataset?: (dataset: UserDataFineTuneResponseDto & { provider?: ProviderFineTuneEnum }) => void;
  onEditDataset?: (dataset: UserDataFineTuneResponseDto & { provider?: ProviderFineTuneEnum }) => void;
  onDeleteDataset?: (dataset: UserDataFineTuneResponseDto & { provider?: ProviderFineTuneEnum }) => void;
}

/**
 * Component hiển thị danh sách Datasets dạng grid
 * Sử dụng ResponsiveGrid để tự động điều chỉnh số cột dựa trên kích thước màn hình
 *
 * Responsive:
 * - Mobile (<640px): 1 column
 * - Small Tablet (640px-767px): 1-2 columns
 * - Tablet (768px-1023px): 2 columns
 * - Desktop (1024px-1279px): 2-3 columns
 * - Large Desktop (≥1280px): 3-4 columns
 */
const DatasetGrid: React.FC<DatasetGridProps> = ({ 
  datasets, 
  onViewDataset, 
  onEditDataset, 
  onDeleteDataset 
}) => {

  return (
    <ResponsiveGrid
      maxColumns={{ xs: 1, sm: 2, md: 2, lg: 3, xl: 4 }}
      maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 2, lg: 2, xl: 3 }}
      gap={{ xs: 4, md: 5, lg: 6 }}
    >
      {datasets.map(dataset => (
        <div key={dataset.id} className="h-full">
          <DatasetCard
            dataset={dataset}
            onClick={() => onViewDataset?.(dataset)}
            onEdit={() => onEditDataset?.(dataset)}
            onDelete={() => onDeleteDataset?.(dataset)}
          />
        </div>
      ))}
    </ResponsiveGrid>
  );
};

export default DatasetGrid;
