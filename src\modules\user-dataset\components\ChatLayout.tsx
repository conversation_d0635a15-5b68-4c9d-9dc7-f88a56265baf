import React, { useState, useCallback } from 'react';
import { ImportedConversation, DatasetMessage } from '../../model-training/types/dataset.types';
import ConversationSidebar from './ConversationSidebar';
import ChatPanel from './ChatPanel';
import { useTranslation } from 'react-i18next';

interface ChatLayoutProps {
  /**
   * Callback khi có thay đổi conversations
   */
  onConversationsChange?: (conversations: ImportedConversation[]) => void;
}

/**
 * Component layout chính cho chat interface
 */
const ChatLayout: React.FC<ChatLayoutProps> = ({ onConversationsChange }) => {
  const { t } = useTranslation();

  // State
  const [conversations, setConversations] = useState<ImportedConversation[]>([]);
  const [selectedConversationId, setSelectedConversationId] = useState<string | null>(null);
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);

  // Get selected conversation
  const selectedConversation = conversations.find(conv => conv.id === selectedConversationId);

  // Generate unique ID
  const generateId = (): string => {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  };

  // Generate conversation title from first user message
  const generateTitle = (messages: DatasetMessage[]): string => {
    const firstUserMessage = messages.find(msg => msg.role === 'user');
    if (firstUserMessage) {
      // Lấy 30 ký tự đầu để title ngắn gọn hơn
      return firstUserMessage.content.length > 30
        ? firstUserMessage.content.substring(0, 30) + '...'
        : firstUserMessage.content;
    }
    return 'New Conversation';
  };

  // Handle import file
  const handleImportFile = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const file = event.target.files?.[0];
      if (!file) return;

      console.log('📁 File selected:', file.name);

      const reader = new FileReader();
      reader.onload = e => {
        try {
          const content = e.target?.result as string;
          const isJsonl = file.name.toLowerCase().endsWith('.jsonl');

          console.log('📄 File content length:', content.length);
          console.log('🔍 Is JSONL:', isJsonl);

          if (isJsonl) {
            // Parse JSONL format
            const lines = content.trim().split('\n');
            console.log('📝 Total lines:', lines.length);

            const newConversations: ImportedConversation[] = [];

            lines.forEach((line, index) => {
              try {
                const trimmedLine = line.trim();
                if (trimmedLine) {
                  console.log(
                    `🔄 Parsing line ${index + 1}:`,
                    trimmedLine.substring(0, 100) + '...'
                  );

                  const jsonObj = JSON.parse(trimmedLine);
                  if (jsonObj.messages && Array.isArray(jsonObj.messages)) {
                    const conversation: ImportedConversation = {
                      id: generateId(),
                      title: generateTitle(jsonObj.messages),
                      messages: jsonObj.messages,
                      createdAt: new Date(),
                    };
                    newConversations.push(conversation);
                    console.log(`✅ Added conversation ${index + 1}:`, conversation.title);
                  } else {
                    console.warn(`⚠️ Line ${index + 1} missing messages array`);
                  }
                }
              } catch (lineError) {
                console.error(`❌ Error parsing line ${index + 1}:`, lineError);
              }
            });

            console.log('📊 Total conversations parsed:', newConversations.length);

            if (newConversations.length > 0) {
              setConversations(prev => {
                const updated = [...prev, ...newConversations];
                console.log('🔄 Updated conversations count:', updated.length);
                return updated;
              });

              // Auto select first imported conversation
              setSelectedConversationId(newConversations[0].id);

              // Notify parent
              onConversationsChange?.(conversations.concat(newConversations));

              console.log(
                `🎉 Successfully imported ${newConversations.length} conversations from JSONL`
              );
            } else {
              console.warn('⚠️ No conversations were imported');
            }
          } else {
            // Parse JSON format (legacy)
            const json = JSON.parse(content);
            if (json.conversations && Array.isArray(json.conversations)) {
              const newConversations: ImportedConversation[] = json.conversations.map(
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                (conv: any) => ({
                  id: generateId(),
                  title: generateTitle(conv.messages),
                  messages: conv.messages,
                  createdAt: new Date(),
                })
              );

              setConversations(prev => [...prev, ...newConversations]);
              if (newConversations.length > 0) {
                setSelectedConversationId(newConversations[0].id);
              }

              onConversationsChange?.(conversations.concat(newConversations));
            }
          }
        } catch (error) {
          console.error('Error parsing file:', error);
          alert('Error parsing file. Please check the file format.');
        }
      };

      reader.readAsText(file);
      // Reset input
      event.target.value = '';
    },
    [conversations, onConversationsChange]
  );

  // Handle select conversation
  const handleSelectConversation = useCallback((conversationId: string) => {
    setSelectedConversationId(conversationId);
  }, []);

  // Handle delete conversation
  const handleDeleteConversation = useCallback(
    (conversationId: string) => {
      setConversations(prev => {
        const updated = prev.filter(conv => conv.id !== conversationId);

        // If deleted conversation was selected, select another one
        if (selectedConversationId === conversationId) {
          setSelectedConversationId(updated.length > 0 ? updated[0].id : null);
        }

        onConversationsChange?.(updated);
        return updated;
      });
    },
    [selectedConversationId, onConversationsChange]
  );

  // Handle toggle sidebar
  const handleToggleSidebar = useCallback(() => {
    setIsSidebarOpen(prev => !prev);
  }, []);

  // Handle new chat
  const handleNewChat = useCallback(() => {
    const newConversation: ImportedConversation = {
      id: generateId(),
      title: 'New Chat',
      messages: [],
      createdAt: new Date(),
    };

    setConversations(prev => {
      const updated = [newConversation, ...prev];
      onConversationsChange?.(updated);
      return updated;
    });

    setSelectedConversationId(newConversation.id);
  }, [onConversationsChange]);

  // Handle add message to selected conversation
  const handleAddMessage = useCallback(
    (message: DatasetMessage) => {
      if (!selectedConversationId) return;

      setConversations(prev => {
        const updated = prev.map(conv => {
          if (conv.id === selectedConversationId) {
            const newMessages = [...conv.messages, message];

            // Cập nhật title nếu đây là message đầu tiên và là user message
            let newTitle = conv.title;
            if (conv.messages.length === 0 && message.role === 'user') {
              newTitle = generateTitle([message]);
            }

            return {
              ...conv,
              title: newTitle,
              messages: newMessages,
            };
          }
          return conv;
        });

        onConversationsChange?.(updated);
        return updated;
      });
    },
    [selectedConversationId, onConversationsChange]
  );

  // Handle delete message from selected conversation
  const handleDeleteMessage = useCallback(
    (messageIndex: number) => {
      if (!selectedConversationId) return;

      setConversations(prev => {
        const updated = prev.map(conv => {
          if (conv.id === selectedConversationId) {
            const newMessages = [...conv.messages];
            newMessages.splice(messageIndex, 1);
            return {
              ...conv,
              messages: newMessages,
            };
          }
          return conv;
        });

        onConversationsChange?.(updated);
        return updated;
      });
    },
    [selectedConversationId, onConversationsChange]
  );

  // Handle edit message in selected conversation
  const handleEditMessage = useCallback(
    (messageIndex: number, message: DatasetMessage) => {
      if (!selectedConversationId) return;

      setConversations(prev => {
        const updated = prev.map(conv => {
          if (conv.id === selectedConversationId) {
            const newMessages = [...conv.messages];
            newMessages[messageIndex] = message;
            return {
              ...conv,
              messages: newMessages,
            };
          }
          return conv;
        });

        onConversationsChange?.(updated);
        return updated;
      });
    },
    [selectedConversationId, onConversationsChange]
  );

  return (
    <div className="h-full flex relative">
      {/* Sidebar */}
      <div
        className={`transition-all duration-300 flex-shrink-0 ${isSidebarOpen ? 'w-80' : 'w-0'}`}
      >
        <ConversationSidebar
          conversations={conversations}
          selectedConversationId={selectedConversationId}
          onSelectConversation={handleSelectConversation}
          onDeleteConversation={handleDeleteConversation}
          onImportFile={handleImportFile}
          onNewChat={handleNewChat}
          isOpen={isSidebarOpen}
          onToggleSidebar={handleToggleSidebar}
        />
      </div>

      {/* Main Content */}
      <div className="flex-1 min-w-0 h-full">
        {selectedConversation ? (
          <div className="h-full w-full">
            <ChatPanel
              title={selectedConversation.title}
              messages={selectedConversation.messages}
              onAddMessage={handleAddMessage}
              onDeleteMessage={handleDeleteMessage}
              onEditMessage={handleEditMessage}
              placeholder="Nhập tin nhắn..."
            />
          </div>
        ) : (
          <div className="h-full flex items-center justify-center">
            <div className="text-center">
              <div className="text-4xl mb-3">💬</div>
              <h3 className="text-lg font-semibold mb-2 text-gray-900 dark:text-white">
                {t('Chọn conversation để bắt đầu')}
              </h3>
              <p className="text-gray-500 dark:text-gray-400 text-sm max-w-sm">
                {t('Import file JSONL hoặc chọn một conversation từ sidebar để xem messages')}
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ChatLayout;
