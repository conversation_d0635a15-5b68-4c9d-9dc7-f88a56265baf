import React from 'react';
import { Card, Chip, IconCard, Tooltip } from '@/shared/components/common';
import { useTranslation } from 'react-i18next';
import {
  UserDataFineTuneResponseDto,
  DataFineTuneStatus,
  ProviderFineTuneEnum,
} from '../user-data-fine-tune/types/user-data-fine-tune.types';

interface DatasetCardProps {
  dataset: UserDataFineTuneResponseDto & { provider?: ProviderFineTuneEnum };
  onClick?: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
  className?: string;
}

const DatasetCard: React.FC<DatasetCardProps> = ({
  dataset,
  onClick,
  onEdit,
  onDelete,
  className = '',
}) => {
  const { t } = useTranslation();

  // Status chip variants
  const getStatusVariant = (status: DataFineTuneStatus) => {
    switch (status) {
      case DataFineTuneStatus.COMPLETED:
        return 'success';
      case DataFineTuneStatus.PROCESSING:
        return 'warning';
      case DataFineTuneStatus.FAILED:
        return 'danger';
      case DataFineTuneStatus.CANCELLED:
        return 'default';
      default:
        return 'info';
    }
  };

  // Status labels
  const getStatusLabel = (status: DataFineTuneStatus) => {
    switch (status) {
      case DataFineTuneStatus.PENDING:
        return t('Đang chờ');
      case DataFineTuneStatus.PROCESSING:
        return t('Đang xử lý');
      case DataFineTuneStatus.COMPLETED:
        return t('Hoàn thành');
      case DataFineTuneStatus.FAILED:
        return t('Thất bại');
      case DataFineTuneStatus.CANCELLED:
        return t('Đã hủy');
      default:
        return status;
    }
  };

  // Provider labels
  const getProviderLabel = (provider?: ProviderFineTuneEnum) => {
    if (!provider) return 'Unknown';
    switch (provider) {
      case ProviderFineTuneEnum.OPENAI:
        return 'OpenAI';
      case ProviderFineTuneEnum.ANTHROPIC:
        return 'Anthropic';
      case ProviderFineTuneEnum.GOOGLE:
        return 'Google';
      default:
        return provider;
    }
  };

  return (
    <Card
      className={`h-full overflow-hidden shadow-md hover:shadow-lg transition-shadow duration-300 ${className}`}
      variant="elevated"
      onClick={onClick}
    >
      <div className="p-3">
        {/* Header: Icon + Tên + Status */}
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2 min-w-0 flex-1">
            {/* Icon nhỏ */}
            <div className="w-8 h-8 flex-shrink-0 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center">
              <span className="text-sm">🛠️</span>
            </div>

            {/* Tên dataset */}
            <h3 className="font-medium text-sm text-gray-900 dark:text-white truncate">
              {dataset.name}
            </h3>
          </div>

          {/* Status chip nhỏ */}
          <Chip
            variant={getStatusVariant(dataset.status)}
            size="sm"
            className="text-xs font-medium flex-shrink-0"
          >
            {getStatusLabel(dataset.status)}
          </Chip>
        </div>

        {/* Mô tả ngắn */}
        <div className="text-xs text-gray-600 dark:text-gray-400 mb-3 line-clamp-2 leading-relaxed">
          {dataset.description || t('Không có mô tả')}
        </div>

        {/* Footer: Actions */}
        <div className="flex items-center justify-between">
          {/* Provider info nhỏ */}
          <div className="text-xs text-gray-500 dark:text-gray-400">
            {getProviderLabel(dataset.provider)}
          </div>

          {/* Action buttons nhỏ */}
          <div className="flex items-center space-x-1">
            {onEdit && (
              <Tooltip content={t('Chỉnh sửa')} position="top">
                <IconCard
                  icon="edit"
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    onEdit();
                  }}
                  disabled={dataset.status === DataFineTuneStatus.PROCESSING}
                />
              </Tooltip>
            )}

            {onDelete && (
              <Tooltip content={t('Xóa')} position="top">
                <IconCard
                  icon="trash"
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    onDelete();
                  }}
                  disabled={dataset.status === DataFineTuneStatus.PROCESSING}
                />
              </Tooltip>
            )}
          </div>
        </div>
      </div>
    </Card>
  );
};

export default DatasetCard;
