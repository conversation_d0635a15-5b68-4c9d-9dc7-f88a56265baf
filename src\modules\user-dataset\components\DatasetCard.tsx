import React from 'react';
import { Card, Chip, IconCard, Tooltip } from '@/shared/components/common';
import { useTranslation } from 'react-i18next';
import {
  UserDataFineTuneResponseDto,
  DataFineTuneStatus,
  ProviderFineTuneEnum,
} from '../user-data-fine-tune/types/user-data-fine-tune.types';

interface DatasetCardProps {
  dataset: UserDataFineTuneResponseDto & { provider?: ProviderFineTuneEnum };
  onClick?: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
  onDownload?: () => void;
  onDuplicate?: () => void;
  className?: string;
}

const DatasetCard: React.FC<DatasetCardProps> = ({
  dataset,
  onClick,
  onEdit,
  onDelete,
  onDownload,
  onDuplicate,
  className = '',
}) => {
  const { t } = useTranslation();

  // Status chip variants
  const getStatusVariant = (status: DataFineTuneStatus) => {
    switch (status) {
      case DataFineTuneStatus.COMPLETED:
        return 'success';
      case DataFineTuneStatus.PROCESSING:
        return 'warning';
      case DataFineTuneStatus.FAILED:
        return 'danger';
      case DataFineTuneStatus.CANCELLED:
        return 'default';
      default:
        return 'info';
    }
  };

  // Status labels
  const getStatusLabel = (status: DataFineTuneStatus) => {
    switch (status) {
      case DataFineTuneStatus.PENDING:
        return t('Đang chờ');
      case DataFineTuneStatus.PROCESSING:
        return t('Đang xử lý');
      case DataFineTuneStatus.COMPLETED:
        return t('Hoàn thành');
      case DataFineTuneStatus.FAILED:
        return t('Thất bại');
      case DataFineTuneStatus.CANCELLED:
        return t('Đã hủy');
      default:
        return status;
    }
  };

  // Provider labels
  const getProviderLabel = (provider?: ProviderFineTuneEnum) => {
    if (!provider) return 'Unknown';
    switch (provider) {
      case ProviderFineTuneEnum.OPENAI:
        return 'OpenAI';
      case ProviderFineTuneEnum.ANTHROPIC:
        return 'Anthropic';
      case ProviderFineTuneEnum.GOOGLE:
        return 'Google';
      default:
        return provider;
    }
  };

  // Format date
  const formatDate = (timestamp: number) => {
    const time = Number(timestamp);
    return isNaN(time) ? 'N/A' : new Date(time).toLocaleString();
  };

  return (
    <Card
      className={`h-full overflow-hidden shadow-md hover:shadow-lg transition-shadow duration-300 ${className}`}
      variant="elevated"
    >
      <div className="p-4">
        <div className="flex flex-col space-y-4">
          {/* Hàng 1: Icon, tên và trạng thái */}
          <div className="flex items-center gap-3 overflow-hidden">
            {/* Icon dataset */}
            <div className="relative w-12 h-12 flex-shrink-0 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
              <span className="text-xl">📊</span>
            </div>

            {/* Thông tin dataset: tên và trạng thái */}
            <div className="flex flex-col min-w-0 flex-grow">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-1 sm:gap-2">
                <div className="min-w-0">
                  <h3 className="font-semibold text-gray-900 dark:text-white truncate">
                    {dataset.name}
                  </h3>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    {formatDate(dataset.createdAt)}
                  </div>
                </div>
                <div className="flex-shrink-0 mt-1 sm:mt-0">
                  <Chip
                    variant={getStatusVariant(dataset.status)}
                    size="sm"
                    className="font-medium"
                  >
                    {getStatusLabel(dataset.status)}
                  </Chip>
                </div>
              </div>
            </div>
          </div>

          {/* Hàng 2: Mô tả */}
          <div className="text-sm text-gray-600 dark:text-gray-300 line-clamp-2">
            {dataset.description || t('Không có mô tả')}
          </div>

          {/* Hàng 3: Provider và các nút chức năng */}
          <div className="flex justify-between items-center">
            <div className="text-xs text-gray-500 dark:text-gray-400 flex items-center gap-1">
              <span>{t('Provider')}:</span>
              <span className="font-medium">{getProviderLabel(dataset.provider)}</span>
            </div>

            {/* Các nút chức năng */}
            <div className="flex justify-end space-x-2">
              <Tooltip content={t('Xem chi tiết')} position="top">
                <IconCard icon="eye" variant="default" size="sm" onClick={onClick} />
              </Tooltip>

              {onEdit && (
                <Tooltip content={t('Chỉnh sửa')} position="top">
                  <IconCard
                    icon="edit"
                    variant="default"
                    size="sm"
                    onClick={onEdit}
                    disabled={dataset.status === DataFineTuneStatus.PROCESSING}
                  />
                </Tooltip>
              )}

              {onDuplicate && (
                <Tooltip content={t('Nhân bản')} position="top">
                  <IconCard icon="copy" variant="default" size="sm" onClick={onDuplicate} />
                </Tooltip>
              )}

              {onDownload && (
                <Tooltip content={t('Tải xuống')} position="top">
                  <IconCard
                    icon="download"
                    variant="default"
                    size="sm"
                    onClick={onDownload}
                    disabled={dataset.status !== DataFineTuneStatus.COMPLETED}
                  />
                </Tooltip>
              )}

              {onDelete && (
                <Tooltip content={t('Xóa')} position="top">
                  <IconCard
                    icon="trash"
                    variant="default"
                    size="sm"
                    onClick={onDelete}
                    disabled={dataset.status === DataFineTuneStatus.PROCESSING}
                  />
                </Tooltip>
              )}
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default DatasetCard;
