import React from 'react';
import { <PERSON>, Typography, Chip, Button, Dropdown } from '@/shared/components/common';
import { useTranslation } from 'react-i18next';
import { Calendar, User, MoreVertical, Edit, Trash2, Download, Eye, Copy } from 'lucide-react';
import {
  UserDataFineTuneResponseDto,
  DataFineTuneStatus,
  ProviderFineTuneEnum,
} from '../user-data-fine-tune/types/user-data-fine-tune.types';

interface DatasetCardProps {
  dataset: UserDataFineTuneResponseDto;
  onClick?: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
  onDownload?: () => void;
  onDuplicate?: () => void;
  className?: string;
}

const DatasetCard: React.FC<DatasetCardProps> = ({
  dataset,
  onClick,
  onEdit,
  onDelete,
  onDownload,
  onDuplicate,
  className = '',
}) => {
  const { t } = useTranslation();

  // Status chip colors
  const getStatusColor = (status: DataFineTuneStatus) => {
    switch (status) {
      case DataFineTuneStatus.COMPLETED:
        return 'success';
      case DataFineTuneStatus.PROCESSING:
        return 'warning';
      case DataFineTuneStatus.FAILED:
        return 'error';
      case DataFineTuneStatus.CANCELLED:
        return 'default';
      default:
        return 'info';
    }
  };

  // Status labels
  const getStatusLabel = (status: DataFineTuneStatus) => {
    switch (status) {
      case DataFineTuneStatus.PENDING:
        return t('Đang chờ');
      case DataFineTuneStatus.PROCESSING:
        return t('Đang xử lý');
      case DataFineTuneStatus.COMPLETED:
        return t('Hoàn thành');
      case DataFineTuneStatus.FAILED:
        return t('Thất bại');
      case DataFineTuneStatus.CANCELLED:
        return t('Đã hủy');
      default:
        return status;
    }
  };

  // Provider labels
  const getProviderLabel = (provider: ProviderFineTuneEnum) => {
    switch (provider) {
      case ProviderFineTuneEnum.OPENAI:
        return 'OpenAI';
      case ProviderFineTuneEnum.ANTHROPIC:
        return 'Anthropic';
      case ProviderFineTuneEnum.GOOGLE:
        return 'Google';
      default:
        return provider;
    }
  };

  // Provider colors
  const getProviderColor = (provider: ProviderFineTuneEnum) => {
    switch (provider) {
      case ProviderFineTuneEnum.OPENAI:
        return 'text-green-600 bg-green-50';
      case ProviderFineTuneEnum.ANTHROPIC:
        return 'text-orange-600 bg-orange-50';
      case ProviderFineTuneEnum.GOOGLE:
        return 'text-blue-600 bg-blue-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  // Format date
  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('vi-VN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // Dropdown menu items
  const menuItems = [
    {
      key: 'view',
      label: t('Xem chi tiết'),
      icon: <Eye size={16} />,
      onClick: onClick,
    },
    {
      key: 'edit',
      label: t('Chỉnh sửa'),
      icon: <Edit size={16} />,
      onClick: onEdit,
      disabled: dataset.status === DataFineTuneStatus.PROCESSING,
    },
    {
      key: 'duplicate',
      label: t('Nhân bản'),
      icon: <Copy size={16} />,
      onClick: onDuplicate,
    },
    {
      key: 'download',
      label: t('Tải xuống'),
      icon: <Download size={16} />,
      onClick: onDownload,
      disabled: dataset.status !== DataFineTuneStatus.COMPLETED,
    },
    {
      key: 'divider',
      type: 'divider',
    },
    {
      key: 'delete',
      label: t('Xóa'),
      icon: <Trash2 size={16} />,
      onClick: onDelete,
      danger: true,
      disabled: dataset.status === DataFineTuneStatus.PROCESSING,
    },
  ];

  return (
    <Card className={`hover:shadow-lg transition-all duration-200 group relative ${className}`}>
      <div className="p-6">
        {/* Header with Actions */}
        <div className="flex justify-between items-start mb-4">
          <div className="flex-1 min-w-0 mr-3">
            <Typography
              variant="h6"
              className="font-semibold text-gray-900 truncate cursor-pointer hover:text-primary transition-colors"
              title={dataset.name}
              onClick={onClick}
            >
              {dataset.name}
            </Typography>
          </div>

          <div className="flex items-center space-x-2 flex-shrink-0">
            <Chip color={getStatusColor(dataset.status)} size="sm" className="font-medium">
              {getStatusLabel(dataset.status)}
            </Chip>

            <Dropdown menu={{ items: menuItems }} trigger={['click']} placement="bottomRight">
              <Button
                variant="ghost"
                size="sm"
                className="opacity-0 group-hover:opacity-100 transition-opacity p-1"
              >
                <MoreVertical size={16} />
              </Button>
            </Dropdown>
          </div>
        </div>

        {/* Description */}
        {dataset.description && (
          <Typography
            variant="body2"
            className="text-gray-600 mb-4 line-clamp-2 leading-relaxed"
            title={dataset.description}
          >
            {dataset.description}
          </Typography>
        )}

        {/* Provider Badge */}
        <div className="flex items-center mb-3">
          <div
            className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium ${getProviderColor(dataset.provider as ProviderFineTuneEnum)}`}
          >
            <User size={12} className="mr-1" />
            {getProviderLabel(dataset.provider as ProviderFineTuneEnum)}
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between pt-3 border-t border-gray-100">
          <div className="flex items-center text-gray-500">
            <Calendar size={14} className="mr-2" />
            <Typography variant="body2" className="text-sm">
              {formatDate(dataset.createdAt)}
            </Typography>
          </div>

          {/* Processing indicator */}
          {dataset.status === DataFineTuneStatus.PROCESSING && (
            <div className="flex items-center">
              <div className="animate-spin rounded-full h-4 w-4 border-2 border-primary border-t-transparent mr-2"></div>
              <Typography variant="body2" className="text-primary text-sm">
                {t('Đang xử lý...')}
              </Typography>
            </div>
          )}

          {/* Success indicator */}
          {dataset.status === DataFineTuneStatus.COMPLETED && (
            <div className="flex items-center text-green-600">
              <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
              <Typography variant="body2" className="text-sm">
                {t('Sẵn sàng')}
              </Typography>
            </div>
          )}

          {/* Error indicator */}
          {dataset.status === DataFineTuneStatus.FAILED && (
            <div className="flex items-center text-red-600">
              <div className="w-2 h-2 bg-red-500 rounded-full mr-2"></div>
              <Typography variant="body2" className="text-sm">
                {t('Lỗi')}
              </Typography>
            </div>
          )}
        </div>
      </div>

      {/* Click overlay */}
      {onClick && (
        <div
          className="absolute inset-0 cursor-pointer"
          onClick={onClick}
          aria-label={`Xem chi tiết ${dataset.name}`}
        />
      )}
    </Card>
  );
};

export default DatasetCard;
