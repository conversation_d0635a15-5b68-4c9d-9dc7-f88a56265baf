import React from 'react';
import { useNavigate } from 'react-router-dom';

import DatasetFormGoogle from '../components/DatasetFormGoogle';

/**
 * Page để tạo dataset cho Google
 * Sử dụng DatasetFormGoogle (không có ConversationSidebar)
 */
const CreateDatasetGooglePage: React.FC = () => {
  const navigate = useNavigate();

  const handleSuccess = () => {
    // Navigate back to dataset list
    navigate('/user-dataset/data-fine-tune');
  };

  return (
    <div className="h-screen flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
        <div className="flex items-center space-x-2">
          <div className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400">
            <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
            Google Provider
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-hidden">
        <DatasetFormGoogle onSuccess={handleSuccess} />
      </div>
    </div>
  );
};

export default CreateDatasetGooglePage;
