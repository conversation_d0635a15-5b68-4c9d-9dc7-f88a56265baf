import React from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/shared/components/common';
import { ArrowLeft } from 'lucide-react';
import DatasetFormGoogle from '../components/DatasetFormGoogle';

/**
 * Page để tạo dataset cho Google
 * Sử dụng DatasetFormGoogle (không có ConversationSidebar)
 */
const CreateDatasetGooglePage: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  const handleSuccess = () => {
    // Navigate back to dataset list
    navigate('/user-dataset/data-fine-tune');
  };

  const handleCancel = () => {
    navigate('/user-dataset/data-fine-tune');
  };

  return (
    <div className="h-screen flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={handleCancel}
            className="flex items-center"
          >
            <ArrowLeft size={16} className="mr-2" />
            {t('Quay lại')}
          </Button>
          
          <div>
            <h1 className="text-xl font-semibold text-gray-900 dark:text-white">
              {t('Tạo Dataset Google')}
            </h1>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {t('Giao diện đơn giản cho Google AI (không có sidebar)')}
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <div className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400">
            <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
            Google Provider
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-hidden">
        <DatasetFormGoogle 
          onSuccess={handleSuccess} 
          onCancel={handleCancel}
        />
      </div>
    </div>
  );
};

export default CreateDatasetGooglePage;
