import { z } from 'zod';
import { KeyLlmProvider, KeyLlmStatus, UserKeyLlmSortBy } from '../types/user-key-llm.types';

/**
 * Schema cho việc tạo key LLM
 */
export const createUserKeyLlmSchema = z.object({
  name: z
    .string()
    .min(1, 'Tên key không được để trống')
    .max(100, 'Tên key không được quá 100 ký tự')
    .regex(/^[a-zA-Z0-9\s\-_]+$/, 'Tên key chỉ được chứa chữ cái, số, dấu gạch ngang và gạch dưới'),

  description: z
    .string()
    .max(500, '<PERSON>ô tả không được quá 500 ký tự')
    .optional(),

  provider: z.nativeEnum(KeyLlmProvider, {
    errorMap: () => ({ message: '<PERSON>hà cung cấp không hợp lệ' }),
  }),

  apiKey: z
    .string()
    .min(1, 'API key không được để trống')
    .min(10, 'API key phải có ít nhất 10 ký tự')
    .max(500, 'API key không được quá 500 ký tự'),

  endpoint: z
    .string()
    .url('Endpoint phải là URL hợp lệ')
    .optional()
    .or(z.literal('')),

  config: z
    .record(z.unknown())
    .optional(),
});

/**
 * Schema cho việc cập nhật key LLM
 */
export const updateUserKeyLlmSchema = z.object({
  name: z
    .string()
    .min(1, 'Tên key không được để trống')
    .max(100, 'Tên key không được quá 100 ký tự')
    .regex(/^[a-zA-Z0-9\s\-_]+$/, 'Tên key chỉ được chứa chữ cái, số, dấu gạch ngang và gạch dưới')
    .optional(),

  description: z
    .string()
    .max(500, 'Mô tả không được quá 500 ký tự')
    .optional(),

  apiKey: z
    .string()
    .min(1, 'API key không được để trống')
    .min(10, 'API key phải có ít nhất 10 ký tự')
    .max(500, 'API key không được quá 500 ký tự')
    .optional(),

  endpoint: z
    .string()
    .url('Endpoint phải là URL hợp lệ')
    .optional()
    .or(z.literal('')),

  config: z
    .record(z.unknown())
    .optional(),

  status: z.nativeEnum(KeyLlmStatus, {
    errorMap: () => ({ message: 'Trạng thái không hợp lệ' }),
  }).optional(),
});

/**
 * Schema cho query parameters
 */
export const userKeyLlmQuerySchema = z.object({
  page: z
    .number()
    .int('Số trang phải là số nguyên')
    .min(1, 'Số trang phải lớn hơn 0')
    .optional()
    .default(1),

  limit: z
    .number()
    .int('Số lượng item phải là số nguyên')
    .min(1, 'Số lượng item phải lớn hơn 0')
    .max(100, 'Số lượng item không được quá 100')
    .optional()
    .default(10),

  search: z
    .string()
    .max(100, 'Từ khóa tìm kiếm không được quá 100 ký tự')
    .optional(),

  provider: z.nativeEnum(KeyLlmProvider).optional(),

  status: z.nativeEnum(KeyLlmStatus).optional(),

  sortBy: z.nativeEnum(UserKeyLlmSortBy).optional().default(UserKeyLlmSortBy.CREATED_AT),

  sortDirection: z
    .enum(['ASC', 'DESC'], {
      errorMap: () => ({ message: 'Hướng sắp xếp phải là ASC hoặc DESC' }),
    })
    .optional()
    .default('DESC'),
});

/**
 * Schema cho việc test key
 */
export const testKeySchema = z.object({
  message: z
    .string()
    .max(1000, 'Message test không được quá 1000 ký tự')
    .optional()
    .default('Hello, this is a test message.'),

  model: z
    .string()
    .max(100, 'Tên model không được quá 100 ký tự')
    .optional(),
});

/**
 * Schema cho ID parameter
 */
export const keyIdSchema = z.object({
  id: z
    .string()
    .uuid('ID phải là UUID hợp lệ'),
});

/**
 * Schema validation cho provider-specific config
 */
export const providerConfigSchemas = {
  [KeyLlmProvider.OPENAI]: z.object({
    organization: z.string().optional(),
    project: z.string().optional(),
    baseURL: z.string().url().optional(),
    defaultHeaders: z.record(z.string()).optional(),
    timeout: z.number().positive().optional(),
    maxRetries: z.number().int().min(0).max(10).optional(),
  }),

  [KeyLlmProvider.ANTHROPIC]: z.object({
    baseURL: z.string().url().optional(),
    defaultHeaders: z.record(z.string()).optional(),
    timeout: z.number().positive().optional(),
    maxRetries: z.number().int().min(0).max(10).optional(),
  }),

  [KeyLlmProvider.GOOGLE]: z.object({
    projectId: z.string().optional(),
    location: z.string().optional(),
    baseURL: z.string().url().optional(),
    timeout: z.number().positive().optional(),
    maxRetries: z.number().int().min(0).max(10).optional(),
  }),

  [KeyLlmProvider.AZURE]: z.object({
    resourceName: z.string().optional(),
    deploymentName: z.string().optional(),
    apiVersion: z.string().optional(),
    baseURL: z.string().url().optional(),
    timeout: z.number().positive().optional(),
    maxRetries: z.number().int().min(0).max(10).optional(),
  }),
};

/**
 * Type inference từ schemas
 */
export type CreateUserKeyLlmInput = z.infer<typeof createUserKeyLlmSchema>;
export type UpdateUserKeyLlmInput = z.infer<typeof updateUserKeyLlmSchema>;
export type UserKeyLlmQueryInput = z.infer<typeof userKeyLlmQuerySchema>;
export type TestKeyInput = z.infer<typeof testKeySchema>;
export type KeyIdInput = z.infer<typeof keyIdSchema>;

/**
 * Helper function để validate provider config
 */
export const validateProviderConfig = (provider: KeyLlmProvider, config: unknown) => {
  const schema = providerConfigSchemas[provider];
  return schema.safeParse(config);
};
