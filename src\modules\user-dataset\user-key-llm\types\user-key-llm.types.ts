/**
 * Enums cho User Key LLM
 */
export enum KeyLlmProvider {
  OPENAI = 'OPENAI',
  ANTHROPIC = 'ANTHROPIC',
  GOOGLE = 'GOOGLE',
  AZURE = 'AZURE',
}

export enum KeyLlmStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  EXPIRED = 'EXPIRED',
  INVALID = 'INVALID',
}

export enum UserKeyLlmSortBy {
  CREATED_AT = 'createdAt',
  NAME = 'name',
  PROVIDER = 'provider',
  STATUS = 'status',
}

/**
 * DTO cho việc tạo key LLM
 */
export interface CreateUserKeyLlmDto {
  /**
   * Tên key
   */
  name: string;

  /**
   * Mô tả key
   */
  description?: string;

  /**
   * Nhà cung cấp
   */
  provider: KeyLlmProvider;

  /**
   * API key
   */
  apiKey: string;

  /**
   * Endpoint URL (nếu cần)
   */
  endpoint?: string;

  /**
   * <PERSON><PERSON><PERSON> hình bổ sung
   */
  config?: Record<string, unknown>;
}

/**
 * DTO cho việc cập nhật key LLM
 */
export interface UpdateUserKeyLlmDto {
  /**
   * Tên key
   */
  name?: string;

  /**
   * Mô tả key
   */
  description?: string;

  /**
   * API key
   */
  apiKey?: string;

  /**
   * Endpoint URL (nếu cần)
   */
  endpoint?: string;

  /**
   * Cấu hình bổ sung
   */
  config?: Record<string, unknown>;

  /**
   * Trạng thái
   */
  status?: KeyLlmStatus;
}

/**
 * Response cho key LLM
 */
export interface UserKeyLlmResponseDto {
  /**
   * ID của key
   */
  id: string;

  /**
   * Tên key
   */
  name: string;

  /**
   * Mô tả key
   */
  description: string | null;

  /**
   * Nhà cung cấp
   */
  provider: KeyLlmProvider;

  /**
   * Trạng thái
   */
  status: KeyLlmStatus;

  /**
   * Thời gian tạo (epoch timestamp)
   */
  createdAt: number;

  /**
   * Thời gian cập nhật cuối (epoch timestamp)
   */
  updatedAt: number;

  /**
   * API key đã được mask
   */
  maskedApiKey: string;

  /**
   * Endpoint URL
   */
  endpoint?: string;

  /**
   * Thời gian sử dụng cuối
   */
  lastUsedAt?: number;

  /**
   * Số lần sử dụng
   */
  usageCount: number;
}

/**
 * Response chi tiết cho key LLM
 */
export interface UserKeyLlmDetailResponseDto extends UserKeyLlmResponseDto {
  /**
   * Cấu hình bổ sung
   */
  config?: Record<string, unknown>;

  /**
   * Thống kê sử dụng
   */
  usageStats?: {
    totalRequests: number;
    totalTokens: number;
    totalCost: number;
    lastMonthRequests: number;
    lastMonthTokens: number;
    lastMonthCost: number;
  };
}

/**
 * Query DTO cho key LLM
 */
export interface UserKeyLlmQueryDto {
  /**
   * Số trang (bắt đầu từ 1)
   */
  page?: number;

  /**
   * Số lượng item trên mỗi trang
   */
  limit?: number;

  /**
   * Từ khóa tìm kiếm
   */
  search?: string;

  /**
   * Tìm kiếm theo nhà cung cấp
   */
  provider?: KeyLlmProvider;

  /**
   * Tìm kiếm theo trạng thái
   */
  status?: KeyLlmStatus;

  /**
   * Trường sắp xếp
   */
  sortBy?: UserKeyLlmSortBy;

  /**
   * Hướng sắp xếp
   */
  sortDirection?: 'ASC' | 'DESC';
}

/**
 * DTO cho việc test key
 */
export interface TestKeyDto {
  /**
   * Message để test
   */
  message?: string;

  /**
   * Model để test (nếu có)
   */
  model?: string;
}

/**
 * Response cho việc test key
 */
export interface TestKeyResponseDto {
  /**
   * Kết quả test
   */
  success: boolean;

  /**
   * Thông báo
   */
  message: string;

  /**
   * Response từ API (nếu thành công)
   */
  response?: string;

  /**
   * Thời gian phản hồi (ms)
   */
  responseTime?: number;

  /**
   * Thông tin model
   */
  modelInfo?: {
    name: string;
    maxTokens: number;
    supportedFeatures: string[];
  };

  /**
   * Lỗi chi tiết (nếu có)
   */
  error?: {
    code: string;
    message: string;
    details?: Record<string, unknown>;
  };
}

/**
 * Response cho usage statistics
 */
export interface KeyUsageStatsResponseDto {
  /**
   * Thống kê theo ngày (30 ngày gần nhất)
   */
  dailyStats: Array<{
    date: string;
    requests: number;
    tokens: number;
    cost: number;
  }>;

  /**
   * Tổng thống kê
   */
  totalStats: {
    totalRequests: number;
    totalTokens: number;
    totalCost: number;
    averageRequestsPerDay: number;
    averageTokensPerRequest: number;
  };

  /**
   * Thống kê theo model
   */
  modelStats: Array<{
    model: string;
    requests: number;
    tokens: number;
    cost: number;
    percentage: number;
  }>;
}

/**
 * Paginated result interface
 */
export interface PaginatedResult<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}
